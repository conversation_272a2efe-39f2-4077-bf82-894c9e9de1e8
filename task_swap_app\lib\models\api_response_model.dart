class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final Map<String, dynamic>? errors;
  final int? statusCode;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.errors,
    this.statusCode,
  });

  factory ApiResponse.success({
    T? data,
    String? message,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error({
    String? message,
    Map<String, dynamic>? errors,
    int? statusCode,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      errors: errors,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>)? fromJsonT,
  ) {
    try {
      final bool success = json['success'] as bool? ?? true;
      
      T? data;
      if (json['data'] != null && fromJsonT != null) {
        if (json['data'] is List) {
          // Handle list data
          final List<dynamic> dataList = json['data'] as List<dynamic>;
          data = dataList.map((item) => fromJsonT(item as Map<String, dynamic>)).toList() as T;
        } else if (json['data'] is Map<String, dynamic>) {
          // Handle single object data
          data = fromJsonT(json['data'] as Map<String, dynamic>);
        }
      } else if (json['data'] != null) {
        data = json['data'] as T;
      }

      return ApiResponse<T>(
        success: success,
        data: data,
        message: json['message'] as String?,
        errors: json['errors'] as Map<String, dynamic>?,
        statusCode: json['status_code'] as int?,
      );
    } catch (e) {
      return ApiResponse<T>.error(
        message: 'Failed to parse API response: $e',
        statusCode: 500,
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data,
      'message': message,
      'errors': errors,
      'status_code': statusCode,
    };
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, statusCode: $statusCode)';
  }
}

class PaginatedResponse<T> {
  final List<T> results;
  final int count;
  final String? next;
  final String? previous;

  PaginatedResponse({
    required this.results,
    required this.count,
    this.next,
    this.previous,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    final List<dynamic> resultsJson = json['results'] as List<dynamic>;
    final List<T> results = resultsJson
        .map((item) => fromJsonT(item as Map<String, dynamic>))
        .toList();

    return PaginatedResponse<T>(
      results: results,
      count: json['count'] as int,
      next: json['next'] as String?,
      previous: json['previous'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'results': results,
      'count': count,
      'next': next,
      'previous': previous,
    };
  }

  bool get hasNext => next != null;
  bool get hasPrevious => previous != null;

  @override
  String toString() {
    return 'PaginatedResponse(count: $count, results: ${results.length})';
  }
}
