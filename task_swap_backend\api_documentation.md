
# TaskSwap - Backend Documentation (Django REST Framework)

## Overview
This Django backend serves the TaskSwap app with user authentication, task management, and task swap functionality. Built with Django 5.2 and Django REST Framework with JWT authentication.

## Project Structure
```
task_swap_backend/
├── task_swap_backend/
│   ├── settings.py
│   ├── urls.py
│   ├── wsgi.py
│   └── asgi.py
├── users/
│   ├── models.py
│   ├── serializers.py
│   ├── views.py
│   ├── urls.py
│   ├── admin.py
│   └── migrations/
├── tasks/
│   ├── models.py
│   ├── serializers.py
│   ├── views.py
│   ├── urls.py
│   ├── admin.py
│   └── migrations/
├── swaps/
│   ├── models.py
│   ├── serializers.py
│   ├── views.py
│   ├── urls.py
│   ├── admin.py
│   └── migrations/
├── manage.py
└── db.sqlite3
```

## Apps
- `users`: Handles user registration, authentication, and profile management
- `tasks`: Handles task CRUD operations and assignments with group-based filtering
- `swaps`: Manages task swap requests between users

## Models

### users.models.User (extends AbstractUser)
- **id**: Auto-generated primary key
- **username**: Django's default username field
- **email**: Unique email address (used as login field)
- **name**: User's display name (CharField, max_length=100, optional)
- **group**: User's group or team (CharField, max_length=100, optional)
- **points**: Points earned from completing tasks (IntegerField, default=0)
- **password**: Hashed password
- **is_active, is_staff, is_superuser**: Django's default user flags
- **date_joined**: Account creation timestamp

### tasks.models.Task
- **id**: Auto-generated primary key
- **title**: Task title (CharField, max_length=200)
- **description**: Task description (TextField, optional)
- **created_by**: ForeignKey to User (who created the task)
- **assigned_to**: ForeignKey to User (who is assigned the task, optional)
- **due_date**: Task deadline (DateTimeField, optional)
- **priority**: Task priority (CharField, choices: low/medium/high/urgent, default=medium)
- **status**: Task status (CharField, choices: pending/in_progress/completed/cancelled, default=pending)
- **created_at**: Task creation timestamp
- **updated_at**: Last modification timestamp

### swaps.models.SwapRequest
- **id**: Auto-generated primary key
- **from_task**: ForeignKey to Task (task being offered for swap)
- **to_task**: ForeignKey to Task (task being requested)
- **from_user**: ForeignKey to User (user initiating the swap)
- **to_user**: ForeignKey to User (user receiving the swap request)
- **status**: Request status (CharField, choices: pending/accepted/declined/cancelled, default=pending)
- **message**: Optional message for the swap request (TextField, optional)
- **created_at**: Request creation timestamp
- **updated_at**: Last modification timestamp
- **Unique constraint**: (from_task, to_task, from_user, to_user)

## Authentication & Security
- **JWT Authentication**: Using `djangorestframework-simplejwt`
- **Access Token Lifetime**: 60 minutes
- **Refresh Token Lifetime**: 7 days
- **Token Rotation**: Enabled (new refresh token on each refresh)
- **Login Field**: Email address (not username)
- **Permissions**: `IsAuthenticated` required for all endpoints except registration and login
- **CORS**: Configured for localhost:3000 and 127.0.0.1:3000
- **Custom User Model**: `users.User` extends Django's AbstractUser

## API Endpoints

### Authentication Endpoints
- **POST** `/api/register/` - User registration
  - Body: `{email, username, name, group, password, password_confirm}`
  - Returns: `{user, access, refresh}` tokens
  - Permission: AllowAny

- **POST** `/api/login/` - User login
  - Body: `{email, password}`
  - Returns: `{user, access, refresh}` tokens
  - Permission: AllowAny

- **POST** `/api/token/refresh/` - Refresh access token
  - Body: `{refresh}`
  - Returns: `{access}` token
  - Permission: AllowAny

- **GET/PUT/PATCH** `/api/profile/` - User profile management
  - Returns/Updates: `{id, email, username, name, group, points, date_joined}`
  - Permission: IsAuthenticated

### Task Management Endpoints
- **GET** `/api/tasks/` - List tasks (group tasks + assigned tasks)
  - Returns: Paginated list of tasks from user's group or assigned to user
  - Permission: IsAuthenticated

- **POST** `/api/tasks/` - Create new task
  - Body: `{title, description, assigned_to, due_date, priority, status}`
  - Permission: IsAuthenticated

- **GET** `/api/tasks/<id>/` - Get task details
  - Returns: Full task details with user information
  - Permission: IsAuthenticated (creator or assignee only)

- **PUT/PATCH** `/api/tasks/<id>/` - Update task
  - Body: `{title, description, assigned_to, due_date, priority, status}`
  - Permission: IsAuthenticated (creator or assignee only)

- **DELETE** `/api/tasks/<id>/` - Delete task
  - Permission: IsAuthenticated (creator or assignee only)

- **GET** `/api/tasks/my-tasks/` - List tasks assigned to current user
  - Permission: IsAuthenticated

- **GET** `/api/tasks/created-tasks/` - List tasks created by current user
  - Permission: IsAuthenticated

### Task Swap Endpoints
- **POST** `/api/swap/` - Create swap request
  - Body: `{from_task_id, to_task_id, message}`
  - Permission: IsAuthenticated (must be assigned to from_task)

- **GET** `/api/swap/requests/` - List all swap requests (sent + received)
  - Permission: IsAuthenticated

- **GET** `/api/swap/incoming/` - List incoming swap requests
  - Permission: IsAuthenticated

- **GET** `/api/swap/outgoing/` - List outgoing swap requests
  - Permission: IsAuthenticated

- **POST** `/api/swap/<id>/respond/` - Accept or decline swap request
  - Body: `{action}` (accept/decline)
  - Permission: IsAuthenticated (recipient only)

## Admin Panel
All models are registered in Django Admin with custom configurations:

- **User Admin**: List display includes email, username, name, group, points, status
- **Task Admin**: List display includes title, creator, assignee, priority, status, due date
- **SwapRequest Admin**: List display includes tasks, users, status, creation date

Access at: `http://127.0.0.1:8000/admin/`
Superuser credentials: <EMAIL> / admin123

## Business Logic Features

### Task Filtering
- Users see tasks from their group OR tasks assigned to them
- Group-based task visibility ensures team collaboration
- Personal task assignment for individual responsibility

### Swap Request Logic
- Users can only swap tasks they are assigned to
- Target task must be assigned to someone
- Prevents duplicate swap requests for same task pair
- Automatic task assignment exchange on acceptance
- Unique constraint prevents duplicate requests

### Security Features
- Email-based authentication
- JWT token rotation
- User can only access/modify their own tasks or assigned tasks
- CORS protection for frontend integration

## Development Setup

### Requirements
- Python 3.8+
- Django 5.2
- Django REST Framework 3.15.2
- djangorestframework-simplejwt 5.3.0
- django-cors-headers 4.3.1

### Running the Server
```bash
python manage.py runserver
```
Server runs at: `http://127.0.0.1:8000/`

### Database
- Development: SQLite3 (db.sqlite3)
- Production: PostgreSQL recommended

## API Response Examples

### Registration Response
```json
{
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "user123",
    "name": "John Doe",
    "group": "Development",
    "points": 0,
    "date_joined": "2025-07-09T18:26:00Z"
  },
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### Task List Response
```json
{
  "count": 10,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "title": "Fix login bug",
      "description": "Users can't login with email",
      "created_by_name": "John Doe",
      "assigned_to_name": "Jane Smith",
      "due_date": "2025-07-15T10:00:00Z",
      "priority": "high",
      "status": "in_progress",
      "created_at": "2025-07-09T18:26:00Z"
    }
  ]
}
```

## Deployment
- **Recommended Platforms**: Render, Railway, Heroku, or VPS with Nginx + Gunicorn
- **Database**: PostgreSQL for production
- **Environment Variables**: Configure SECRET_KEY, DATABASE_URL, ALLOWED_HOSTS
- **Static Files**: Configure for production deployment
- **CORS**: Update CORS_ALLOWED_ORIGINS for production frontend URL
