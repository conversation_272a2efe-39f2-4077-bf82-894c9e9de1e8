from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q
from .models import SwapRequest
from .serializers import (
    SwapRequestSerializer,
    SwapRequestCreateSerializer,
    SwapRequestResponseSerializer
)


class SwapRequestCreateView(generics.CreateAPIView):
    """Create a new swap request"""
    serializer_class = SwapRequestCreateSerializer
    permission_classes = [permissions.IsAuthenticated]


class SwapRequestListView(generics.ListAPIView):
    """List swap requests for the current user"""
    serializer_class = SwapRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        # Return both sent and received swap requests
        return SwapRequest.objects.filter(
            Q(from_user=user) | Q(to_user=user)
        ).select_related('from_task', 'to_task', 'from_user', 'to_user')


class IncomingSwapRequestsView(generics.ListAPIView):
    """List incoming swap requests for the current user"""
    serializer_class = SwapRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return SwapRequest.objects.filter(
            to_user=self.request.user,
            status='pending'
        ).select_related('from_task', 'to_task', 'from_user', 'to_user')


class OutgoingSwapRequestsView(generics.ListAPIView):
    """List outgoing swap requests from the current user"""
    serializer_class = SwapRequestSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return SwapRequest.objects.filter(
            from_user=self.request.user
        ).select_related('from_task', 'to_task', 'from_user', 'to_user')


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def respond_to_swap(request, pk):
    """Accept or decline a swap request"""
    swap_request = get_object_or_404(SwapRequest, pk=pk, to_user=request.user)

    if swap_request.status != 'pending':
        return Response(
            {'error': 'This swap request has already been processed'},
            status=status.HTTP_400_BAD_REQUEST
        )

    serializer = SwapRequestResponseSerializer(data=request.data)
    if serializer.is_valid():
        action = serializer.validated_data['action']

        if action == 'accept':
            swap_request.accept()
            message = 'Swap request accepted successfully'
        else:
            swap_request.decline()
            message = 'Swap request declined'

        return Response({
            'message': message,
            'swap_request': SwapRequestSerializer(swap_request).data
        }, status=status.HTTP_200_OK)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
