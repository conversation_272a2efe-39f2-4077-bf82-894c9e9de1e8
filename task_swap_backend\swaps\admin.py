from django.contrib import admin
from .models import SwapRequest


@admin.register(SwapRequest)
class SwapRequestAdmin(admin.ModelAdmin):
    """Admin configuration for SwapRequest model"""
    list_display = ('from_task', 'to_task', 'from_user', 'to_user', 'status', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('from_task__title', 'to_task__title', 'from_user__email', 'to_user__email')
    list_editable = ('status',)
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)

    fieldsets = (
        (None, {
            'fields': ('from_task', 'to_task', 'message')
        }),
        ('Users', {
            'fields': ('from_user', 'to_user')
        }),
        ('Status', {
            'fields': ('status',)
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
