# Generated by Django 5.2 on 2025-07-09 18:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('swaps', '0002_initial'),
        ('tasks', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='swaprequest',
            name='from_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='swap_requests_sent', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='swaprequest',
            name='to_task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='swap_requests_to', to='tasks.task'),
        ),
        migrations.AddField(
            model_name='swaprequest',
            name='to_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='swap_requests_received', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterUniqueTogether(
            name='swaprequest',
            unique_together={('from_task', 'to_task', 'from_user', 'to_user')},
        ),
    ]
