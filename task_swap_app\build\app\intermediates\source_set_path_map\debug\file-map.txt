com.example.task_swap_app-preference-1.2.1-0 C:\Users\<USER>\.gradle\caches\transforms-3\047aa7ddff6241681155a5178fa0e58d\transformed\preference-1.2.1\res
com.example.task_swap_app-jetified-lifecycle-livedata-core-ktx-2.7.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\051f9dac57ad8cee60cc5a95eb8d9d7f\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.example.task_swap_app-jetified-datastore-preferences-release-2 C:\Users\<USER>\.gradle\caches\transforms-3\05f0f5bac33a554f70bb00c44aed0318\transformed\jetified-datastore-preferences-release\res
com.example.task_swap_app-media-1.1.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\07b47b454deb98a7092e3e2891f674ce\transformed\media-1.1.0\res
com.example.task_swap_app-jetified-activity-ktx-1.8.1-4 C:\Users\<USER>\.gradle\caches\transforms-3\0efada588904821f11adf7ec39ee67b0\transformed\jetified-activity-ktx-1.8.1\res
com.example.task_swap_app-lifecycle-runtime-2.7.0-5 C:\Users\<USER>\.gradle\caches\transforms-3\12d0e8d71b0600cec5ba2edcf29e905c\transformed\lifecycle-runtime-2.7.0\res
com.example.task_swap_app-jetified-lifecycle-viewmodel-savedstate-2.7.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\2b2bf4b21be1acb6d8aafc6e5e4bedac\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.example.task_swap_app-coordinatorlayout-1.0.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\334f77f688c510e4c631ba2daaab7ec9\transformed\coordinatorlayout-1.0.0\res
com.example.task_swap_app-core-runtime-2.2.0-8 C:\Users\<USER>\.gradle\caches\transforms-3\3cccf64d8e44a1ff0116ea6b8bd8d8e9\transformed\core-runtime-2.2.0\res
com.example.task_swap_app-jetified-profileinstaller-1.3.1-9 C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\res
com.example.task_swap_app-jetified-lifecycle-process-2.7.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\res
com.example.task_swap_app-jetified-savedstate-1.2.1-11 C:\Users\<USER>\.gradle\caches\transforms-3\40a45e686cd0e99ac2a33b1453f47939\transformed\jetified-savedstate-1.2.1\res
com.example.task_swap_app-jetified-lifecycle-viewmodel-ktx-2.7.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\4b65a3e10bc06490e1a05d1d300300eb\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.example.task_swap_app-jetified-datastore-release-13 C:\Users\<USER>\.gradle\caches\transforms-3\5078db51dda9483ea086ca5bb7d61476\transformed\jetified-datastore-release\res
com.example.task_swap_app-jetified-core-1.0.0-14 C:\Users\<USER>\.gradle\caches\transforms-3\61fbac9cdc1a22c54aa04ec76334a907\transformed\jetified-core-1.0.0\res
com.example.task_swap_app-lifecycle-livedata-core-2.7.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\6964f45bcdaad7c523c03595346078c4\transformed\lifecycle-livedata-core-2.7.0\res
com.example.task_swap_app-appcompat-1.1.0-16 C:\Users\<USER>\.gradle\caches\transforms-3\6ddfe7bc5a026c54878de2bc1391ef46\transformed\appcompat-1.1.0\res
com.example.task_swap_app-lifecycle-viewmodel-2.7.0-17 C:\Users\<USER>\.gradle\caches\transforms-3\7b5a837770709484bd30395a2788a4e5\transformed\lifecycle-viewmodel-2.7.0\res
com.example.task_swap_app-jetified-appcompat-resources-1.1.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\7f4a0c54f8ded54a4c0d55036a334d3c\transformed\jetified-appcompat-resources-1.1.0\res
com.example.task_swap_app-lifecycle-livedata-2.7.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\82d8b2a7d9fca5e641d160069565b477\transformed\lifecycle-livedata-2.7.0\res
com.example.task_swap_app-jetified-lifecycle-runtime-ktx-2.7.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\85a2d865d0e12a22249976e5601901a3\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.example.task_swap_app-jetified-window-1.2.0-21 C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\res
com.example.task_swap_app-core-1.13.1-22 C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\res
com.example.task_swap_app-jetified-window-java-1.2.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\8a650b2c5ea333036303d473a939db42\transformed\jetified-window-java-1.2.0\res
com.example.task_swap_app-jetified-fragment-ktx-1.7.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\8ab227ca6d562dab86394f7a2b42fe97\transformed\jetified-fragment-ktx-1.7.1\res
com.example.task_swap_app-jetified-core-ktx-1.13.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\8b9f2b08a8ef59d66d7e2f702c5ee30e\transformed\jetified-core-ktx-1.13.1\res
com.example.task_swap_app-transition-1.4.1-26 C:\Users\<USER>\.gradle\caches\transforms-3\8e926d88adc6ec4405f4b01d4e9b1bad\transformed\transition-1.4.1\res
com.example.task_swap_app-recyclerview-1.0.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\9005ab99f1717e9b0608d9f5ab54ff23\transformed\recyclerview-1.0.0\res
com.example.task_swap_app-jetified-activity-1.8.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\90da4fa7ab29bed17775e89b3905b1f1\transformed\jetified-activity-1.8.1\res
com.example.task_swap_app-jetified-startup-runtime-1.1.1-29 C:\Users\<USER>\.gradle\caches\transforms-3\a1fd92b89d0c194f8fe8fdca4d20e2ee\transformed\jetified-startup-runtime-1.1.1\res
com.example.task_swap_app-slidingpanelayout-1.2.0-30 C:\Users\<USER>\.gradle\caches\transforms-3\a499fa3c34a38edcfdea6b351554e382\transformed\slidingpanelayout-1.2.0\res
com.example.task_swap_app-jetified-annotation-experimental-1.4.0-31 C:\Users\<USER>\.gradle\caches\transforms-3\c4537ab9015740d05f09a422063523a5\transformed\jetified-annotation-experimental-1.4.0\res
com.example.task_swap_app-fragment-1.7.1-32 C:\Users\<USER>\.gradle\caches\transforms-3\ca4d40f38b71d4c621d36eba6fe2f4a4\transformed\fragment-1.7.1\res
com.example.task_swap_app-jetified-security-crypto-1.1.0-alpha06-33 C:\Users\<USER>\.gradle\caches\transforms-3\de258cad7dbb6e789f5ae62dc60121e0\transformed\jetified-security-crypto-1.1.0-alpha06\res
com.example.task_swap_app-jetified-datastore-core-release-34 C:\Users\<USER>\.gradle\caches\transforms-3\e066f98655f1455a1140912311a34f0d\transformed\jetified-datastore-core-release\res
com.example.task_swap_app-jetified-tracing-1.2.0-35 C:\Users\<USER>\.gradle\caches\transforms-3\e896b9ac412a8814559a776dc7b5a420\transformed\jetified-tracing-1.2.0\res
com.example.task_swap_app-jetified-savedstate-ktx-1.2.1-36 C:\Users\<USER>\.gradle\caches\transforms-3\eb30b97cf0c123a3aa78bfba9082ceea\transformed\jetified-savedstate-ktx-1.2.1\res
com.example.task_swap_app-debug-37 E:\Programs\task_swap\task_swap_app\android\app\src\debug\res
com.example.task_swap_app-main-38 E:\Programs\task_swap\task_swap_app\android\app\src\main\res
com.example.task_swap_app-pngs-39 E:\Programs\task_swap\task_swap_app\build\app\generated\res\pngs\debug
com.example.task_swap_app-resValues-40 E:\Programs\task_swap\task_swap_app\build\app\generated\res\resValues\debug
com.example.task_swap_app-packageDebugResources-41 E:\Programs\task_swap\task_swap_app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.task_swap_app-packageDebugResources-42 E:\Programs\task_swap\task_swap_app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.task_swap_app-merged_res-43 E:\Programs\task_swap\task_swap_app\build\app\intermediates\merged_res\debug
com.example.task_swap_app-packaged_res-44 E:\Programs\task_swap\task_swap_app\build\flutter_local_notifications\intermediates\packaged_res\debug
com.example.task_swap_app-packaged_res-45 E:\Programs\task_swap\task_swap_app\build\flutter_secure_storage\intermediates\packaged_res\debug
com.example.task_swap_app-packaged_res-46 E:\Programs\task_swap\task_swap_app\build\fluttertoast\intermediates\packaged_res\debug
com.example.task_swap_app-packaged_res-47 E:\Programs\task_swap\task_swap_app\build\path_provider_android\intermediates\packaged_res\debug
com.example.task_swap_app-packaged_res-48 E:\Programs\task_swap\task_swap_app\build\shared_preferences_android\intermediates\packaged_res\debug
com.example.task_swap_app-packaged_res-49 E:\Programs\task_swap\task_swap_app\build\sqflite_android\intermediates\packaged_res\debug
