import 'dart:io';
import 'package:http/http.dart' as http;

void main() async {
  print('🚀 Testing TaskSwap Frontend-Backend Connection...\n');
  
  const String baseUrl = 'http://127.0.0.1:8000';
  const String apiUrl = '$baseUrl/api';
  
  // Test 1: Basic server connectivity
  print('1️⃣ Testing basic server connectivity...');
  try {
    final response = await http.get(
      Uri.parse('$baseUrl/admin/'),
      headers: {'Accept': 'text/html,application/json'},
    ).timeout(const Duration(seconds: 10));
    
    print('   ✅ Server is reachable! Status: ${response.statusCode}');
  } catch (e) {
    print('   ❌ Server not reachable: $e');
    print('   💡 Make sure Django server is running: python manage.py runserver');
    return;
  }
  
  // Test 2: API endpoints
  print('\n2️⃣ Testing API endpoints...');
  
  final endpoints = [
    {'path': '/register/', 'method': 'GET', 'expected': [405, 200]}, // Method not allowed is OK
    {'path': '/login/', 'method': 'GET', 'expected': [405, 200]},
    {'path': '/tasks/', 'method': 'GET', 'expected': [401]}, // Unauthorized is expected
  ];
  
  for (final endpoint in endpoints) {
    try {
      final url = '$apiUrl${endpoint['path']}';
      print('   Testing: $url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 5));
      
      final expectedCodes = endpoint['expected'] as List<int>;
      if (expectedCodes.contains(response.statusCode)) {
        print('     ✅ Status: ${response.statusCode} (Expected)');
      } else {
        print('     ⚠️  Status: ${response.statusCode} (Unexpected, but endpoint exists)');
      }
    } catch (e) {
      print('     ❌ Error: $e');
    }
  }
  
  // Test 3: CORS headers
  print('\n3️⃣ Testing CORS configuration...');
  try {
    final response = await http.get(
      Uri.parse('$apiUrl/tasks/'),
      headers: {
        'Accept': 'application/json',
        'Origin': 'http://localhost:3000', // Simulate frontend origin
      },
    ).timeout(const Duration(seconds: 5));
    
    final corsHeaders = response.headers;
    print('   CORS Headers:');
    corsHeaders.forEach((key, value) {
      if (key.toLowerCase().contains('access-control')) {
        print('     $key: $value');
      }
    });
    
    if (corsHeaders.containsKey('access-control-allow-origin')) {
      print('   ✅ CORS is configured');
    } else {
      print('   ⚠️  CORS headers not found (might be OK for same-origin)');
    }
  } catch (e) {
    print('   ❌ CORS test failed: $e');
  }
  
  // Test 4: Authentication flow
  print('\n4️⃣ Testing authentication flow...');
  try {
    // Try to register a test user (might fail if user exists)
    final registerResponse = await http.post(
      Uri.parse('$apiUrl/register/'),
      headers: {'Content-Type': 'application/json'},
      body: '{"email":"<EMAIL>","username":"testconn","name":"Test Connection","group":"TestGroup","password":"testpass123","password_confirm":"testpass123"}',
    ).timeout(const Duration(seconds: 10));
    
    if (registerResponse.statusCode == 201) {
      print('   ✅ Registration endpoint works');
    } else if (registerResponse.statusCode == 400) {
      print('   ✅ Registration endpoint works (user might already exist)');
      print('   Response: ${registerResponse.body}');
    } else {
      print('   ⚠️  Registration returned: ${registerResponse.statusCode}');
    }
    
    // Try to login
    final loginResponse = await http.post(
      Uri.parse('$apiUrl/login/'),
      headers: {'Content-Type': 'application/json'},
      body: '{"email":"<EMAIL>","password":"testpass123"}',
    ).timeout(const Duration(seconds: 10));
    
    if (loginResponse.statusCode == 200) {
      print('   ✅ Login endpoint works');
      print('   Response contains: ${loginResponse.body.contains('access') ? 'access token' : 'no token'}');
    } else {
      print('   ⚠️  Login returned: ${loginResponse.statusCode}');
      print('   Response: ${loginResponse.body}');
    }
    
  } catch (e) {
    print('   ❌ Authentication test failed: $e');
  }
  
  print('\n📋 Connection test completed!');
  print('💡 If all tests pass, your frontend and backend are properly connected.');
}
