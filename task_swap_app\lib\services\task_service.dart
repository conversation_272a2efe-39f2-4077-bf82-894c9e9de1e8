import '../models/task_model.dart';
import '../models/api_response_model.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class TaskService {
  // Get all tasks (group tasks and assigned tasks)
  static Future<ApiResponse<List<Task>>> getAllTasks() async {
    try {
      final response = await ApiService.get<List<Map<String, dynamic>>>(
        ApiEndpoints.tasks,
      );

      if (response.success && response.data != null) {
        final tasks = (response.data as List<dynamic>)
            .map((taskJson) => Task.fromJson(taskJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: tasks,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get tasks',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get tasks: $e',
        statusCode: 500,
      );
    }
  }

  // Get tasks assigned to current user
  static Future<ApiResponse<List<Task>>> getMyTasks() async {
    try {
      final response = await ApiService.get<List<Map<String, dynamic>>>(
        ApiEndpoints.myTasks,
      );

      if (response.success && response.data != null) {
        final tasks = (response.data as List<dynamic>)
            .map((taskJson) => Task.fromJson(taskJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: tasks,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get my tasks',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get my tasks: $e',
        statusCode: 500,
      );
    }
  }

  // Get tasks created by current user
  static Future<ApiResponse<List<Task>>> getCreatedTasks() async {
    try {
      final response = await ApiService.get<List<Map<String, dynamic>>>(
        ApiEndpoints.createdTasks,
      );

      if (response.success && response.data != null) {
        final tasks = (response.data as List<dynamic>)
            .map((taskJson) => Task.fromJson(taskJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: tasks,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get created tasks',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get created tasks: $e',
        statusCode: 500,
      );
    }
  }

  // Get task details by ID
  static Future<ApiResponse<Task>> getTaskById(int id) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiEndpoints.taskDetail(id),
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final task = Task.fromJson(response.data!);

        return ApiResponse.success(
          data: task,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get task details',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get task details: $e',
        statusCode: 500,
      );
    }
  }

  // Create new task
  static Future<ApiResponse<Task>> createTask({
    required String title,
    required String description,
    int? assignedToId,
    DateTime? dueDate,
    required TaskPriority priority,
    TaskStatus status = TaskStatus.pending,
  }) async {
    try {
      final data = {
        'title': title,
        'description': description,
        'priority': priority.value,
        'status': status.value,
      };

      if (assignedToId != null) {
        data['assigned_to'] = assignedToId.toString();
      }

      if (dueDate != null) {
        data['due_date'] = dueDate.toIso8601String();
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.tasks,
        data,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final task = Task.fromJson(response.data!);

        return ApiResponse.success(
          data: task,
          message: response.message ?? 'Task created successfully',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to create task',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to create task: $e',
        statusCode: 500,
      );
    }
  }

  // Update existing task
  static Future<ApiResponse<Task>> updateTask({
    required int id,
    String? title,
    String? description,
    int? assignedToId,
    DateTime? dueDate,
    TaskPriority? priority,
    TaskStatus? status,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (title != null) data['title'] = title;
      if (description != null) data['description'] = description;
      if (assignedToId != null) data['assigned_to'] = assignedToId.toString();
      if (dueDate != null) data['due_date'] = dueDate.toIso8601String();
      if (priority != null) data['priority'] = priority.value;
      if (status != null) data['status'] = status.value;

      final response = await ApiService.put<Map<String, dynamic>>(
        ApiEndpoints.updateTask(id),
        data,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final task = Task.fromJson(response.data!);

        return ApiResponse.success(
          data: task,
          message: response.message ?? 'Task updated successfully',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to update task',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update task: $e',
        statusCode: 500,
      );
    }
  }

  // Delete task
  static Future<ApiResponse<void>> deleteTask(int id) async {
    try {
      final response = await ApiService.delete<void>(
        ApiEndpoints.deleteTask(id),
      );

      if (response.success) {
        return ApiResponse.success(
          message: response.message ?? 'Task deleted successfully',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to delete task',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to delete task: $e',
        statusCode: 500,
      );
    }
  }

  // Mark task as completed
  static Future<ApiResponse<Task>> markTaskCompleted(int id) async {
    return updateTask(id: id, status: TaskStatus.completed);
  }

  // Mark task as in progress
  static Future<ApiResponse<Task>> markTaskInProgress(int id) async {
    return updateTask(id: id, status: TaskStatus.inProgress);
  }

  // Cancel task
  static Future<ApiResponse<Task>> cancelTask(int id) async {
    return updateTask(id: id, status: TaskStatus.cancelled);
  }
}
