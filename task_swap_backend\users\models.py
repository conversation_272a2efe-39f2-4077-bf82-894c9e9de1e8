from django.contrib.auth.models import AbstractUser
from django.db import models


class User(AbstractUser):
    """Custom User model with additional fields for TaskSwap app"""
    name = models.CharField(max_length=100, blank=True)
    email = models.EmailField(unique=True)
    group = models.CharField(max_length=100, blank=True, help_text="User's group or team")
    points = models.IntegerField(default=0, help_text="Points earned from completing tasks")

    # Use email as the username field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    def __str__(self):
        return self.email

    class Meta:
        db_table = 'users_user'
