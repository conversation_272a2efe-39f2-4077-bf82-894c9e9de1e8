import 'package:flutter/foundation.dart';
import '../models/task_model.dart';
import '../services/task_service.dart';

class TaskProvider with ChangeNotifier {
  List<Task> _allTasks = [];
  List<Task> _myTasks = [];
  List<Task> _createdTasks = [];
  bool _isLoading = false;
  String? _errorMessage;
  Task? _selectedTask;

  // Getters
  List<Task> get allTasks => _allTasks;
  List<Task> get myTasks => _myTasks;
  List<Task> get createdTasks => _createdTasks;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  Task? get selectedTask => _selectedTask;

  // Filtered tasks by status
  List<Task> get pendingTasks =>
      _allTasks.where((task) => task.status == TaskStatus.pending).toList();
  List<Task> get inProgressTasks =>
      _allTasks.where((task) => task.status == TaskStatus.inProgress).toList();
  List<Task> get completedTasks =>
      _allTasks.where((task) => task.status == TaskStatus.completed).toList();

  // Filtered tasks by priority
  List<Task> get urgentTasks =>
      _allTasks.where((task) => task.priority == TaskPriority.urgent).toList();
  List<Task> get highPriorityTasks =>
      _allTasks.where((task) => task.priority == TaskPriority.high).toList();

  // Load all tasks
  Future<void> loadAllTasks() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.getAllTasks();

      if (response.success && response.data != null) {
        _allTasks = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load tasks');
      }
    } catch (e) {
      _setError('Failed to load tasks: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load my tasks
  Future<void> loadMyTasks() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.getMyTasks();

      if (response.success && response.data != null) {
        _myTasks = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load my tasks');
      }
    } catch (e) {
      _setError('Failed to load my tasks: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load created tasks
  Future<void> loadCreatedTasks() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.getCreatedTasks();

      if (response.success && response.data != null) {
        _createdTasks = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load created tasks');
      }
    } catch (e) {
      _setError('Failed to load created tasks: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load task by ID
  Future<void> loadTaskById(int id) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.getTaskById(id);

      if (response.success && response.data != null) {
        _selectedTask = response.data!;

        // Update the task in lists if it exists
        _updateTaskInLists(response.data!);
      } else {
        _setError(response.message ?? 'Failed to load task');
      }
    } catch (e) {
      _setError('Failed to load task: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create new task
  Future<bool> createTask({
    required String title,
    required String description,
    int? assignedToId,
    DateTime? dueDate,
    required TaskPriority priority,
    TaskStatus status = TaskStatus.pending,
  }) async {
    print('🎯 TaskProvider: Creating task with title: $title');
    print(
        '🎯 TaskProvider: Priority: ${priority.value}, Status: ${status.value}');
    print('🎯 TaskProvider: AssignedToId: $assignedToId');

    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.createTask(
        title: title,
        description: description,
        assignedToId: assignedToId,
        dueDate: dueDate,
        priority: priority,
        status: status,
      );

      if (response.success) {
        // Task creation was successful
        if (response.data != null) {
          // Add to appropriate lists if we have the task data
          _allTasks.insert(0, response.data!);
          _createdTasks.insert(0, response.data!);

          if (assignedToId != null) {
            // If assigned to someone, it might be in myTasks too
            // This would depend on the current user ID
          }
        } else {
          // Task was created but we don't have the full data
          // Refresh the task lists to get the latest data
          loadAllTasks();
          loadCreatedTasks();
        }

        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to create task');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to create task: $e');
      _setLoading(false);
      return false;
    }
  }

  // Update task
  Future<bool> updateTask({
    required int id,
    String? title,
    String? description,
    int? assignedToId,
    DateTime? dueDate,
    TaskPriority? priority,
    TaskStatus? status,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.updateTask(
        id: id,
        title: title,
        description: description,
        assignedToId: assignedToId,
        dueDate: dueDate,
        priority: priority,
        status: status,
      );

      if (response.success && response.data != null) {
        _updateTaskInLists(response.data!);
        _selectedTask = response.data!;
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to update task');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to update task: $e');
      _setLoading(false);
      return false;
    }
  }

  // Delete task
  Future<bool> deleteTask(int id) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await TaskService.deleteTask(id);

      if (response.success) {
        _removeTaskFromLists(id);
        if (_selectedTask?.id == id) {
          _selectedTask = null;
        }
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to delete task');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to delete task: $e');
      _setLoading(false);
      return false;
    }
  }

  // Mark task as completed
  Future<bool> markTaskCompleted(int id) async {
    return updateTask(id: id, status: TaskStatus.completed);
  }

  // Mark task as in progress
  Future<bool> markTaskInProgress(int id) async {
    return updateTask(id: id, status: TaskStatus.inProgress);
  }

  // Cancel task
  Future<bool> cancelTask(int id) async {
    return updateTask(id: id, status: TaskStatus.cancelled);
  }

  // Refresh all task lists
  Future<void> refreshAllTasks() async {
    await Future.wait([
      loadAllTasks(),
      loadMyTasks(),
      loadCreatedTasks(),
    ]);
  }

  // Set selected task
  void setSelectedTask(Task? task) {
    _selectedTask = task;
    notifyListeners();
  }

  // Clear selected task
  void clearSelectedTask() {
    _selectedTask = null;
    notifyListeners();
  }

  // Filter tasks by status
  List<Task> getTasksByStatus(TaskStatus status) {
    return _allTasks.where((task) => task.status == status).toList();
  }

  // Filter tasks by priority
  List<Task> getTasksByPriority(TaskPriority priority) {
    return _allTasks.where((task) => task.priority == priority).toList();
  }

  // Search tasks
  List<Task> searchTasks(String query) {
    if (query.isEmpty) return _allTasks;

    final lowercaseQuery = query.toLowerCase();
    return _allTasks.where((task) {
      return task.title.toLowerCase().contains(lowercaseQuery) ||
          task.description.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Private helper methods
  void _updateTaskInLists(Task updatedTask) {
    // Update in all tasks
    final allTaskIndex =
        _allTasks.indexWhere((task) => task.id == updatedTask.id);
    if (allTaskIndex != -1) {
      _allTasks[allTaskIndex] = updatedTask;
    }

    // Update in my tasks
    final myTaskIndex =
        _myTasks.indexWhere((task) => task.id == updatedTask.id);
    if (myTaskIndex != -1) {
      _myTasks[myTaskIndex] = updatedTask;
    }

    // Update in created tasks
    final createdTaskIndex =
        _createdTasks.indexWhere((task) => task.id == updatedTask.id);
    if (createdTaskIndex != -1) {
      _createdTasks[createdTaskIndex] = updatedTask;
    }

    notifyListeners();
  }

  void _removeTaskFromLists(int taskId) {
    _allTasks.removeWhere((task) => task.id == taskId);
    _myTasks.removeWhere((task) => task.id == taskId);
    _createdTasks.removeWhere((task) => task.id == taskId);
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
  }
}
