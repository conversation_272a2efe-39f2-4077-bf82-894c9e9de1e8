from rest_framework import serializers
from .models import SwapRequest
from tasks.serializers import TaskListSerializer
from users.serializers import UserProfileSerializer


class SwapRequestSerializer(serializers.ModelSerializer):
    """Serializer for SwapRequest model"""
    from_task = TaskListSerializer(read_only=True)
    to_task = TaskListSerializer(read_only=True)
    from_user = UserProfileSerializer(read_only=True)
    to_user = UserProfileSerializer(read_only=True)
    
    class Meta:
        model = SwapRequest
        fields = [
            'id', 'from_task', 'to_task', 'from_user', 'to_user', 
            'status', 'message', 'created_at', 'updated_at'
        ]
        read_only_fields = ('id', 'from_user', 'created_at', 'updated_at')


class SwapRequestCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating swap requests"""
    from_task_id = serializers.IntegerField()
    to_task_id = serializers.IntegerField()
    
    class Meta:
        model = SwapRequest
        fields = ['from_task_id', 'to_task_id', 'message']
    
    def validate(self, attrs):
        from_task_id = attrs.get('from_task_id')
        to_task_id = attrs.get('to_task_id')
        
        if from_task_id == to_task_id:
            raise serializers.ValidationError("Cannot swap a task with itself")
        
        # Check if tasks exist and are accessible
        from tasks.models import Task
        user = self.context['request'].user
        
        try:
            from_task = Task.objects.get(id=from_task_id, assigned_to=user)
            to_task = Task.objects.get(id=to_task_id)
        except Task.DoesNotExist:
            raise serializers.ValidationError("One or both tasks not found or not accessible")
        
        # Check if the to_task is assigned to someone
        if not to_task.assigned_to:
            raise serializers.ValidationError("Target task must be assigned to someone")
        
        # Check if swap request already exists
        if SwapRequest.objects.filter(
            from_task=from_task, 
            to_task=to_task, 
            from_user=user, 
            to_user=to_task.assigned_to,
            status='pending'
        ).exists():
            raise serializers.ValidationError("Swap request already exists for these tasks")
        
        attrs['from_task'] = from_task
        attrs['to_task'] = to_task
        attrs['to_user'] = to_task.assigned_to
        
        return attrs
    
    def create(self, validated_data):
        validated_data['from_user'] = self.context['request'].user
        validated_data.pop('from_task_id')
        validated_data.pop('to_task_id')
        return super().create(validated_data)


class SwapRequestResponseSerializer(serializers.Serializer):
    """Serializer for responding to swap requests"""
    action = serializers.ChoiceField(choices=['accept', 'decline'])
    
    def validate_action(self, value):
        if value not in ['accept', 'decline']:
            raise serializers.ValidationError("Action must be 'accept' or 'decline'")
        return value
