class User {
  final int id;
  final String email;
  final String username;
  final String name;
  final String group;
  final int points;
  final DateTime dateJoined;

  User({
    required this.id,
    required this.email,
    required this.username,
    required this.name,
    required this.group,
    required this.points,
    required this.dateJoined,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as int,
      email: json['email'] as String,
      username: json['username'] as String,
      name: json['name'] as String,
      group: json['group'] as String,
      points: json['points'] as int? ?? 0,
      dateJoined: DateTime.parse(json['date_joined'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'username': username,
      'name': name,
      'group': group,
      'points': points,
      'date_joined': dateJoined.toIso8601String(),
    };
  }

  User copyWith({
    int? id,
    String? email,
    String? username,
    String? name,
    String? group,
    int? points,
    DateTime? dateJoined,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      name: name ?? this.name,
      group: group ?? this.group,
      points: points ?? this.points,
      dateJoined: dateJoined ?? this.dateJoined,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, email: $email, username: $username, name: $name, group: $group, points: $points)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
