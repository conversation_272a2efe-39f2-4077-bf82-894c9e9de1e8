import '../models/api_response_model.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class LeaderboardEntry {
  final int rank;
  final int id;
  final String name;
  final String username;
  final String group;
  final int points;
  final bool isCurrentUser;

  LeaderboardEntry({
    required this.rank,
    required this.id,
    required this.name,
    required this.username,
    required this.group,
    required this.points,
    required this.isCurrentUser,
  });

  factory LeaderboardEntry.fromJson(Map<String, dynamic> json) {
    return LeaderboardEntry(
      rank: json['rank'] as int,
      id: json['id'] as int,
      name: json['name'] as String? ?? '',
      username: json['username'] as String? ?? '',
      group: json['group'] as String? ?? '',
      points: json['points'] as int,
      isCurrentUser: json['is_current_user'] as bool? ?? false,
    );
  }
}

class LeaderboardResponse {
  final List<LeaderboardEntry> leaderboard;
  final int? currentUserRank;
  final int totalUsers;

  LeaderboardResponse({
    required this.leaderboard,
    this.currentUserRank,
    required this.totalUsers,
  });

  factory LeaderboardResponse.fromJson(Map<String, dynamic> json) {
    final leaderboardData = json['leaderboard'] as List<dynamic>;
    
    return LeaderboardResponse(
      leaderboard: leaderboardData
          .map((item) => LeaderboardEntry.fromJson(item as Map<String, dynamic>))
          .toList(),
      currentUserRank: json['current_user_rank'] as int?,
      totalUsers: json['total_users'] as int,
    );
  }
}

class LeaderboardService {
  // Get leaderboard data
  static Future<ApiResponse<LeaderboardResponse>> getLeaderboard() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiEndpoints.leaderboard,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final leaderboardResponse = LeaderboardResponse.fromJson(response.data!);

        return ApiResponse.success(
          data: leaderboardResponse,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get leaderboard',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get leaderboard: $e',
        statusCode: 500,
      );
    }
  }
}
