import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/swap_provider.dart';
import '../providers/task_provider.dart';
import '../providers/auth_provider.dart';
import '../models/swap_request_model.dart';
import '../models/task_model.dart';
import '../utils/constants.dart';

class SwapCenterScreen extends StatefulWidget {
  const SwapCenterScreen({super.key});

  @override
  State<SwapCenterScreen> createState() => _SwapCenterScreenState();
}

class _SwapCenterScreenState extends State<SwapCenterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Load swap data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSwapData();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSwapData() async {
    final swapProvider = Provider.of<SwapProvider>(context, listen: false);
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);

    try {
      await Future.wait([
        swapProvider.loadIncomingSwapRequests(),
        swapProvider.loadOutgoingSwapRequests(),
        taskProvider.loadMyTasks(),
        taskProvider.loadAllTasks(),
      ]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load swap data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Swap Center'),
        backgroundColor: const Color(AppColors.primaryColorValue),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSwapData,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Incoming Requests'),
            Tab(text: 'Outgoing Requests'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildIncomingRequestsTab(),
          _buildOutgoingRequestsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateSwapDialog(),
        backgroundColor: const Color(AppColors.primaryColorValue),
        child: const Icon(Icons.swap_horiz, color: Colors.white),
      ),
    );
  }

  Widget _buildIncomingRequestsTab() {
    return Consumer<SwapProvider>(
      builder: (context, swapProvider, child) {
        if (swapProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (swapProvider.incomingSwapRequests.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No incoming swap requests',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadSwapData,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: swapProvider.incomingSwapRequests.length,
            itemBuilder: (context, index) {
              final swapRequest = swapProvider.incomingSwapRequests[index];
              return _buildIncomingSwapCard(swapRequest);
            },
          ),
        );
      },
    );
  }

  Widget _buildOutgoingRequestsTab() {
    return Consumer<SwapProvider>(
      builder: (context, swapProvider, child) {
        if (swapProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (swapProvider.outgoingSwapRequests.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.outbox,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'No outgoing swap requests',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _loadSwapData,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: swapProvider.outgoingSwapRequests.length,
            itemBuilder: (context, index) {
              final swapRequest = swapProvider.outgoingSwapRequests[index];
              return _buildOutgoingSwapCard(swapRequest);
            },
          ),
        );
      },
    );
  }

  Widget _buildIncomingSwapCard(SwapRequest swapRequest) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, size: 20),
                const SizedBox(width: 8),
                Text(
                  'From: ${swapRequest.fromUser.name.isNotEmpty ? swapRequest.fromUser.name : swapRequest.fromUser.username}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                _buildStatusChip(swapRequest.status),
              ],
            ),
            const SizedBox(height: 12),

            // Swap details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('They offer:',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                      Text(
                        swapRequest.fromTask.title,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(
                                  swapRequest.fromTask.priority),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              swapRequest.fromTask.priority.name.toUpperCase(),
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.swap_horiz, color: Colors.blue),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text('For your:',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                      Text(
                        swapRequest.toTask.title,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(
                                  swapRequest.toTask.priority),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              swapRequest.toTask.priority.name.toUpperCase(),
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            if (swapRequest.message?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  swapRequest.message!,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ],

            if (swapRequest.status == SwapStatus.pending) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _acceptSwapRequest(swapRequest),
                      icon: const Icon(Icons.check, color: Colors.white),
                      label: const Text('Accept',
                          style: TextStyle(color: Colors.white)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _declineSwapRequest(swapRequest),
                      icon: const Icon(Icons.close, color: Colors.white),
                      label: const Text('Decline',
                          style: TextStyle(color: Colors.white)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOutgoingSwapCard(SwapRequest swapRequest) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.person, size: 20),
                const SizedBox(width: 8),
                Text(
                  'To: ${swapRequest.toUser.name.isNotEmpty ? swapRequest.toUser.name : swapRequest.toUser.username}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                _buildStatusChip(swapRequest.status),
              ],
            ),
            const SizedBox(height: 12),

            // Swap details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('You offer:',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                      Text(
                        swapRequest.fromTask.title,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(
                                  swapRequest.fromTask.priority),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              swapRequest.fromTask.priority.name.toUpperCase(),
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.swap_horiz, color: Colors.blue),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      const Text('For their:',
                          style: TextStyle(fontSize: 12, color: Colors.grey)),
                      Text(
                        swapRequest.toTask.title,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPriorityColor(
                                  swapRequest.toTask.priority),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              swapRequest.toTask.priority.name.toUpperCase(),
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            if (swapRequest.message?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  swapRequest.message!,
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
            ],

            if (swapRequest.status == SwapStatus.pending) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () => _cancelSwapRequest(swapRequest),
                  icon: const Icon(Icons.cancel, color: Colors.white),
                  label: const Text('Cancel Request',
                      style: TextStyle(color: Colors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(SwapStatus status) {
    Color color;
    String text;

    switch (status) {
      case SwapStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case SwapStatus.accepted:
        color = Colors.green;
        text = 'Accepted';
        break;
      case SwapStatus.declined:
        color = Colors.red;
        text = 'Declined';
        break;
      case SwapStatus.cancelled:
        color = Colors.grey;
        text = 'Cancelled';
        break;
    }

    return Chip(
      label: Text(
        text,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Future<void> _acceptSwapRequest(SwapRequest swapRequest) async {
    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog(
      title: 'Accept Swap Request',
      content:
          'Are you sure you want to accept this swap? This will exchange the task assignments.',
      confirmText: 'Accept',
      confirmColor: Colors.green,
    );

    if (!confirmed || !mounted) return;

    final swapProvider = Provider.of<SwapProvider>(context, listen: false);

    final success = await swapProvider.acceptSwapRequest(swapRequest.id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Swap request accepted successfully! Tasks have been exchanged.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 4),
          ),
        );
        _loadSwapData(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                swapProvider.errorMessage ?? 'Failed to accept swap request'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _declineSwapRequest(SwapRequest swapRequest) async {
    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog(
      title: 'Decline Swap Request',
      content: 'Are you sure you want to decline this swap request?',
      confirmText: 'Decline',
      confirmColor: Colors.orange,
    );

    if (!confirmed || !mounted) return;

    final swapProvider = Provider.of<SwapProvider>(context, listen: false);

    final success = await swapProvider.declineSwapRequest(swapRequest.id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Swap request declined'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        _loadSwapData(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                swapProvider.errorMessage ?? 'Failed to decline swap request'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  Future<void> _cancelSwapRequest(SwapRequest swapRequest) async {
    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog(
      title: 'Cancel Swap Request',
      content: 'Are you sure you want to cancel this swap request?',
      confirmText: 'Cancel Request',
      confirmColor: Colors.red,
    );

    if (!confirmed || !mounted) return;

    final swapProvider = Provider.of<SwapProvider>(context, listen: false);

    final success = await swapProvider.cancelSwapRequest(swapRequest.id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Swap request cancelled'),
            backgroundColor: Colors.orange,
            duration: Duration(seconds: 3),
          ),
        );
        _loadSwapData(); // Refresh data
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                swapProvider.errorMessage ?? 'Failed to cancel swap request'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  void _showCreateSwapDialog() {
    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    // Get tasks assigned to current user (tasks they can offer)
    final myTasks = taskProvider.myTasks
        .where((task) =>
            task.assignedTo?.id == authProvider.user?.id &&
            task.status != TaskStatus.completed)
        .toList();

    // Get all available tasks (tasks they can request)
    final availableTasks = taskProvider.allTasks
        .where((task) =>
            task.assignedTo?.id != authProvider.user?.id &&
            task.status != TaskStatus.completed)
        .toList();

    if (myTasks.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You have no tasks to offer for swap'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (availableTasks.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No tasks available for swap'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _CreateSwapDialog(
        myTasks: myTasks,
        availableTasks: availableTasks,
        onSwapCreated: _loadSwapData,
      ),
    );
  }

  Future<bool> _showConfirmationDialog({
    required String title,
    required String content,
    required String confirmText,
    required Color confirmColor,
  }) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: Text(title),
            content: Text(content),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: confirmColor,
                ),
                child: Text(
                  confirmText,
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.orange;
      case TaskPriority.high:
        return Colors.red;
      case TaskPriority.urgent:
        return Colors.purple;
    }
  }
}

class _CreateSwapDialog extends StatefulWidget {
  final List<Task> myTasks;
  final List<Task> availableTasks;
  final VoidCallback onSwapCreated;

  const _CreateSwapDialog({
    required this.myTasks,
    required this.availableTasks,
    required this.onSwapCreated,
  });

  @override
  State<_CreateSwapDialog> createState() => _CreateSwapDialogState();
}

class _CreateSwapDialogState extends State<_CreateSwapDialog> {
  Task? selectedMyTask;
  Task? selectedTargetTask;
  final TextEditingController messageController = TextEditingController();
  bool isLoading = false;

  @override
  void dispose() {
    messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create Swap Request'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your Task to Offer:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<Task>(
              value: selectedMyTask,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Select your task',
              ),
              items: widget.myTasks.map((task) {
                return DropdownMenuItem(
                  value: task,
                  child: Text(
                    task.title,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: (task) {
                setState(() {
                  selectedMyTask = task;
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Task You Want:'),
            const SizedBox(height: 8),
            DropdownButtonFormField<Task>(
              value: selectedTargetTask,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Select target task',
              ),
              items: widget.availableTasks.map((task) {
                return DropdownMenuItem(
                  value: task,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        task.title,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        'Assigned to: ${task.assignedTo?.name ?? 'Unknown'}',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (task) {
                setState(() {
                  selectedTargetTask = task;
                });
              },
            ),
            const SizedBox(height: 16),
            const Text('Message (Optional):'),
            const SizedBox(height: 8),
            TextField(
              controller: messageController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Add a message to explain why you want to swap...',
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed:
              isLoading || selectedMyTask == null || selectedTargetTask == null
                  ? null
                  : _createSwapRequest,
          child: isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Create Request'),
        ),
      ],
    );
  }

  Future<void> _createSwapRequest() async {
    if (selectedMyTask == null || selectedTargetTask == null) return;

    setState(() {
      isLoading = true;
    });

    final swapProvider = Provider.of<SwapProvider>(context, listen: false);

    final success = await swapProvider.createSwapRequest(
      fromTaskId: selectedMyTask!.id,
      toTaskId: selectedTargetTask!.id,
      message: messageController.text.trim().isEmpty
          ? null
          : messageController.text.trim(),
    );

    if (mounted) {
      setState(() {
        isLoading = false;
      });

      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Swap request created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onSwapCreated();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                swapProvider.errorMessage ?? 'Failed to create swap request'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
