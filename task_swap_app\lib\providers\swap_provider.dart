import 'package:flutter/foundation.dart';
import '../models/swap_request_model.dart';
import '../services/swap_service.dart';

class SwapProvider with ChangeNotifier {
  List<SwapRequest> _allSwapRequests = [];
  List<SwapRequest> _incomingSwapRequests = [];
  List<SwapRequest> _outgoingSwapRequests = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<SwapRequest> get allSwapRequests => _allSwapRequests;
  List<SwapRequest> get incomingSwapRequests => _incomingSwapRequests;
  List<SwapRequest> get outgoingSwapRequests => _outgoingSwapRequests;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Filtered swap requests by status
  List<SwapRequest> get pendingIncomingRequests => _incomingSwapRequests
      .where((swap) => swap.status == SwapStatus.pending)
      .toList();

  List<SwapRequest> get pendingOutgoingRequests => _outgoingSwapRequests
      .where((swap) => swap.status == SwapStatus.pending)
      .toList();

  List<SwapRequest> get acceptedSwapRequests => _allSwapRequests
      .where((swap) => swap.status == SwapStatus.accepted)
      .toList();

  List<SwapRequest> get declinedSwapRequests => _allSwapRequests
      .where((swap) => swap.status == SwapStatus.declined)
      .toList();

  // Count getters
  int get pendingIncomingCount => pendingIncomingRequests.length;
  int get pendingOutgoingCount => pendingOutgoingRequests.length;

  // Load all swap requests
  Future<void> loadAllSwapRequests() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.getAllSwapRequests();

      if (response.success && response.data != null) {
        _allSwapRequests = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load swap requests');
      }
    } catch (e) {
      _setError('Failed to load swap requests: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load incoming swap requests
  Future<void> loadIncomingSwapRequests() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.getIncomingSwapRequests();

      if (response.success && response.data != null) {
        _incomingSwapRequests = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load incoming swap requests');
      }
    } catch (e) {
      _setError('Failed to load incoming swap requests: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load outgoing swap requests
  Future<void> loadOutgoingSwapRequests() async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.getOutgoingSwapRequests();

      if (response.success && response.data != null) {
        _outgoingSwapRequests = response.data!;
      } else {
        _setError(response.message ?? 'Failed to load outgoing swap requests');
      }
    } catch (e) {
      _setError('Failed to load outgoing swap requests: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Create new swap request
  Future<bool> createSwapRequest({
    required int fromTaskId,
    required int toTaskId,
    String? message,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.createSwapRequest(
        fromTaskId: fromTaskId,
        toTaskId: toTaskId,
        message: message,
      );

      if (response.success && response.data != null) {
        // Add to outgoing requests
        _outgoingSwapRequests.insert(0, response.data!);
        _allSwapRequests.insert(0, response.data!);

        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to create swap request');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to create swap request: $e');
      _setLoading(false);
      return false;
    }
  }

  // Accept swap request
  Future<bool> acceptSwapRequest(int swapRequestId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.acceptSwapRequest(swapRequestId);

      if (response.success && response.data != null) {
        _updateSwapRequestInLists(response.data!);
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to accept swap request');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to accept swap request: $e');
      _setLoading(false);
      return false;
    }
  }

  // Decline swap request
  Future<bool> declineSwapRequest(int swapRequestId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.declineSwapRequest(swapRequestId);

      if (response.success && response.data != null) {
        _updateSwapRequestInLists(response.data!);
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to decline swap request');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to decline swap request: $e');
      _setLoading(false);
      return false;
    }
  }

  // Cancel swap request
  Future<bool> cancelSwapRequest(int swapRequestId) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await SwapService.cancelSwapRequest(swapRequestId);

      if (response.success && response.data != null) {
        _updateSwapRequestInLists(response.data!);
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Failed to cancel swap request');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to cancel swap request: $e');
      _setLoading(false);
      return false;
    }
  }

  // Refresh all swap request lists
  Future<void> refreshAllSwapRequests() async {
    await Future.wait([
      loadAllSwapRequests(),
      loadIncomingSwapRequests(),
      loadOutgoingSwapRequests(),
    ]);
  }

  // Get swap requests by status
  List<SwapRequest> getSwapRequestsByStatus(SwapStatus status) {
    return _allSwapRequests.where((swap) => swap.status == status).toList();
  }

  // Search swap requests
  List<SwapRequest> searchSwapRequests(String query) {
    if (query.isEmpty) return _allSwapRequests;

    final lowercaseQuery = query.toLowerCase();
    return _allSwapRequests.where((swap) {
      return swap.fromTask.title.toLowerCase().contains(lowercaseQuery) ||
          swap.toTask.title.toLowerCase().contains(lowercaseQuery) ||
          swap.fromUser.name.toLowerCase().contains(lowercaseQuery) ||
          swap.toUser.name.toLowerCase().contains(lowercaseQuery) ||
          (swap.message?.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // Check if user can create swap request between tasks
  bool canCreateSwapRequest(int fromTaskId, int toTaskId) {
    // Check if there's already a pending swap request between these tasks
    return !_allSwapRequests.any((swap) =>
        swap.status == SwapStatus.pending &&
        ((swap.fromTask.id == fromTaskId && swap.toTask.id == toTaskId) ||
            (swap.fromTask.id == toTaskId && swap.toTask.id == fromTaskId)));
  }

  // Get swap request by ID
  SwapRequest? getSwapRequestById(int id) {
    try {
      return _allSwapRequests.firstWhere((swap) => swap.id == id);
    } catch (e) {
      return null;
    }
  }

  // Private helper methods
  void _updateSwapRequestInLists(SwapRequest updatedSwapRequest) {
    // Update in all swap requests
    final allIndex =
        _allSwapRequests.indexWhere((swap) => swap.id == updatedSwapRequest.id);
    if (allIndex != -1) {
      _allSwapRequests[allIndex] = updatedSwapRequest;
    }

    // Update in incoming swap requests
    final incomingIndex = _incomingSwapRequests
        .indexWhere((swap) => swap.id == updatedSwapRequest.id);
    if (incomingIndex != -1) {
      _incomingSwapRequests[incomingIndex] = updatedSwapRequest;
    }

    // Update in outgoing swap requests
    final outgoingIndex = _outgoingSwapRequests
        .indexWhere((swap) => swap.id == updatedSwapRequest.id);
    if (outgoingIndex != -1) {
      _outgoingSwapRequests[outgoingIndex] = updatedSwapRequest;
    }

    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
  }
}
