import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/constants.dart';
import '../models/api_response_model.dart';

class ApiService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const Duration _timeout =
      Duration(seconds: AppConstants.requestTimeout);

  // Get headers with authentication token
  static Future<Map<String, String>> _getHeaders(
      {bool includeAuth = true}) async {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      final token = await _storage.read(key: AppConstants.accessTokenKey);
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Handle HTTP response and convert to ApiResponse
  static ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>)? fromJson,
  ) {
    try {
      // Log response for debugging
      print('Response Status: ${response.statusCode}');
      print('Response Body: ${response.body}');
      print('Response Headers: ${response.headers}');

      Map<String, dynamic> responseData;

      // Try to parse JSON response
      try {
        responseData = jsonDecode(response.body);
      } catch (e) {
        // If JSON parsing fails, create a wrapper
        responseData = {
          'message':
              response.body.isNotEmpty ? response.body : 'Empty response',
          'raw_response': response.body,
        };
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Success response
        T? data;
        if (fromJson != null && responseData['data'] != null) {
          if (responseData['data'] is List) {
            final List<dynamic> dataList =
                responseData['data'] as List<dynamic>;
            data = dataList
                .map((item) => fromJson(item as Map<String, dynamic>))
                .toList() as T;
          } else if (responseData['data'] is Map<String, dynamic>) {
            data = fromJson(responseData['data'] as Map<String, dynamic>);
          }
        } else if (responseData.containsKey('data')) {
          data = responseData['data'] as T?;
        } else {
          // If no 'data' key, treat the entire response as data
          data = responseData as T?;
        }

        return ApiResponse.success(
          data: data,
          message: responseData['message'] as String?,
          statusCode: response.statusCode,
        );
      } else {
        // Error response - extract error details
        String errorMessage = 'Request failed';
        Map<String, dynamic>? errors;

        if (responseData.containsKey('message')) {
          errorMessage = responseData['message'] as String;
        } else if (responseData.containsKey('error')) {
          errorMessage = responseData['error'].toString();
        } else if (responseData.containsKey('detail')) {
          errorMessage = responseData['detail'].toString();
        }

        // Extract field-specific errors
        if (responseData.containsKey('errors')) {
          errors = responseData['errors'] as Map<String, dynamic>?;
        } else {
          // Check if the entire response is field errors (common in Django REST)
          if (responseData.keys.any(
              (key) => key != 'message' && key != 'error' && key != 'detail')) {
            errors = responseData;
          }
        }

        return ApiResponse.error(
          message: errorMessage,
          errors: errors,
          statusCode: response.statusCode,
        );
      }
    } catch (e) {
      print('Error parsing response: $e');
      return ApiResponse.error(
        message: 'Failed to parse response: $e',
        statusCode: response.statusCode,
      );
    }
  }

  // GET request
  static Future<ApiResponse<T>> get<T>(
    String endpoint, {
    T Function(Map<String, dynamic>)? fromJson,
    bool includeAuth = true,
  }) async {
    try {
      final headers = await _getHeaders(includeAuth: includeAuth);
      final uri = Uri.parse('${AppConstants.apiUrl}$endpoint');

      final response = await http.get(uri, headers: headers).timeout(_timeout);
      return _handleResponse<T>(response, fromJson);
    } on SocketException {
      return ApiResponse.error(
        message: AppStrings.networkError,
        statusCode: 0,
      );
    } on HttpException {
      return ApiResponse.error(
        message: AppStrings.serverError,
        statusCode: 500,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Request failed: $e',
        statusCode: 500,
      );
    }
  }

  // POST request
  static Future<ApiResponse<T>> post<T>(
    String endpoint,
    Map<String, dynamic> data, {
    T Function(Map<String, dynamic>)? fromJson,
    bool includeAuth = true,
  }) async {
    try {
      final headers = await _getHeaders(includeAuth: includeAuth);
      final uri = Uri.parse('${AppConstants.apiUrl}$endpoint');

      // Log request for debugging
      print('POST Request to: $uri');
      print('Request Headers: $headers');
      print('Request Body: ${jsonEncode(data)}');

      final response = await http
          .post(
            uri,
            headers: headers,
            body: jsonEncode(data),
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } on SocketException catch (e) {
      print('SocketException: $e');
      return ApiResponse.error(
        message: AppStrings.networkError,
        statusCode: 0,
      );
    } on HttpException catch (e) {
      print('HttpException: $e');
      return ApiResponse.error(
        message: AppStrings.serverError,
        statusCode: 500,
      );
    } catch (e) {
      print('Request error: $e');
      return ApiResponse.error(
        message: 'Request failed: $e',
        statusCode: 500,
      );
    }
  }

  // PUT request
  static Future<ApiResponse<T>> put<T>(
    String endpoint,
    Map<String, dynamic> data, {
    T Function(Map<String, dynamic>)? fromJson,
    bool includeAuth = true,
  }) async {
    try {
      final headers = await _getHeaders(includeAuth: includeAuth);
      final uri = Uri.parse('${AppConstants.apiUrl}$endpoint');

      final response = await http
          .put(
            uri,
            headers: headers,
            body: jsonEncode(data),
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } on SocketException {
      return ApiResponse.error(
        message: AppStrings.networkError,
        statusCode: 0,
      );
    } on HttpException {
      return ApiResponse.error(
        message: AppStrings.serverError,
        statusCode: 500,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Request failed: $e',
        statusCode: 500,
      );
    }
  }

  // PATCH request
  static Future<ApiResponse<T>> patch<T>(
    String endpoint,
    Map<String, dynamic> data, {
    T Function(Map<String, dynamic>)? fromJson,
    bool includeAuth = true,
  }) async {
    try {
      final headers = await _getHeaders(includeAuth: includeAuth);
      final uri = Uri.parse('${AppConstants.apiUrl}$endpoint');

      final response = await http
          .patch(
            uri,
            headers: headers,
            body: jsonEncode(data),
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } on SocketException {
      return ApiResponse.error(
        message: AppStrings.networkError,
        statusCode: 0,
      );
    } on HttpException {
      return ApiResponse.error(
        message: AppStrings.serverError,
        statusCode: 500,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Request failed: $e',
        statusCode: 500,
      );
    }
  }

  // DELETE request
  static Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T Function(Map<String, dynamic>)? fromJson,
    bool includeAuth = true,
  }) async {
    try {
      final headers = await _getHeaders(includeAuth: includeAuth);
      final uri = Uri.parse('${AppConstants.apiUrl}$endpoint');

      final response =
          await http.delete(uri, headers: headers).timeout(_timeout);
      return _handleResponse<T>(response, fromJson);
    } on SocketException {
      return ApiResponse.error(
        message: AppStrings.networkError,
        statusCode: 0,
      );
    } on HttpException {
      return ApiResponse.error(
        message: AppStrings.serverError,
        statusCode: 500,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Request failed: $e',
        statusCode: 500,
      );
    }
  }

  // Clear stored tokens
  static Future<void> clearTokens() async {
    await _storage.delete(key: AppConstants.accessTokenKey);
    await _storage.delete(key: AppConstants.refreshTokenKey);
    await _storage.delete(key: AppConstants.userDataKey);
  }
}
