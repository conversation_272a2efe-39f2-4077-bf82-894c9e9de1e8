class AppConstants {
  // API Configuration
  // Use 10.0.2.2 for Android emulator, 127.0.0.1 for iOS simulator/physical device
  static const String baseUrl =
      'http://192.168.31.244:8000'; // For testing - works with most setups
  // static const String baseUrl = 'http://10.0.2.2:8000'; // Android emulator
  // static const String baseUrl = 'http://127.0.0.1:8000';  // iOS simulator/physical device
  // static const String baseUrl = 'https://your-api.com';  // Production

  static const String apiUrl = '$baseUrl/api';

  // App constants
  static const String appName = 'TaskSwap';
  static const int requestTimeout = 30; // seconds

  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';

  // Shared Preferences Keys
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String firstLaunchKey = 'first_launch';
}

class ApiEndpoints {
  // Authentication Endpoints
  static const String register = '/register/';
  static const String login = '/login/';
  static const String tokenRefresh = '/token/refresh/';
  static const String profile = '/profile/';
  static const String leaderboard = '/leaderboard/';

  // Task Management Endpoints
  static const String tasks = '/tasks/';
  static const String myTasks = '/tasks/my-tasks/';
  static const String createdTasks = '/tasks/created-tasks/';

  // Swap Management Endpoints
  static const String swap = '/swap/';
  static const String swapRequests = '/swap/requests/';
  static const String incomingSwaps = '/swap/incoming/';
  static const String outgoingSwaps = '/swap/outgoing/';

  // Helper methods for dynamic endpoints
  static String taskDetail(int id) => '/tasks/$id/';
  static String updateTask(int id) => '/tasks/$id/';
  static String deleteTask(int id) => '/tasks/$id/';
  static String swapRespond(int id) => '/swap/$id/respond/';
}

class AppColors {
  // Primary Colors
  static const int primaryColorValue = 0xFF2196F3;
  static const int accentColorValue = 0xFF03DAC6;

  // Task Priority Colors
  static const int lowPriorityColor = 0xFF4CAF50; // Green
  static const int mediumPriorityColor = 0xFFFF9800; // Orange
  static const int highPriorityColor = 0xFFFF5722; // Deep Orange
  static const int urgentPriorityColor = 0xFFF44336; // Red

  // Task Status Colors
  static const int pendingStatusColor = 0xFF9E9E9E; // Grey
  static const int inProgressStatusColor = 0xFF2196F3; // Blue
  static const int completedStatusColor = 0xFF4CAF50; // Green
  static const int cancelledStatusColor = 0xFFF44336; // Red

  // Swap Status Colors
  static const int swapPendingColor = 0xFFFF9800; // Orange
  static const int swapAcceptedColor = 0xFF4CAF50; // Green
  static const int swapDeclinedColor = 0xFFF44336; // Red
  static const int swapCancelledColor = 0xFF9E9E9E; // Grey
}

class AppStrings {
  // App Info
  static const String appName = 'TaskSwap';
  static const String appVersion = '1.0.0';

  // Authentication
  static const String login = 'Login';
  static const String register = 'Register';
  static const String logout = 'Logout';
  static const String email = 'Email';
  static const String password = 'Password';
  static const String confirmPassword = 'Confirm Password';
  static const String username = 'Username';
  static const String name = 'Name';
  static const String group = 'Group';

  // Navigation
  static const String dashboard = 'Dashboard';
  static const String myTasks = 'My Tasks';
  static const String groupTasks = 'Group Tasks';
  static const String createdTasks = 'Created Tasks';
  static const String swapCenter = 'Swap Center';
  static const String leaderboard = 'Leaderboard';
  static const String profile = 'Profile';

  // Task Management
  static const String createTask = 'Create Task';
  static const String editTask = 'Edit Task';
  static const String taskTitle = 'Task Title';
  static const String taskDescription = 'Task Description';
  static const String assignedTo = 'Assigned To';
  static const String dueDate = 'Due Date';
  static const String priority = 'Priority';
  static const String status = 'Status';
  static const String taskDetails = 'Task Details';

  // Swap Management
  static const String createSwap = 'Create Swap';
  static const String incomingRequests = 'Incoming Requests';
  static const String outgoingRequests = 'Outgoing Requests';
  static const String acceptSwap = 'Accept';
  static const String declineSwap = 'Decline';

  // Common Actions
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String refresh = 'Refresh';
  static const String loading = 'Loading...';
  static const String retry = 'Retry';

  // Error Messages
  static const String networkError =
      'Network error. Please check your connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred.';
  static const String validationError =
      'Please check your input and try again.';
  static const String authenticationError =
      'Authentication failed. Please login again.';
}

class AppDimensions {
  // Padding and Margins
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;

  // Border Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;

  // Icon Sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;

  // Button Heights
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;
}
