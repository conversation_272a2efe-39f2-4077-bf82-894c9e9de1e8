1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.task_swap_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:6:5-66
15-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:45:5-50:15
24        <intent>
24-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:46:9-49:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:47:13-72
25-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:47:21-70
26
27            <data android:mimeType="text/plain" />
27-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:48:13-50
27-->E:\Programs\task_swap\task_swap_app\android\app\src\main\AndroidManifest.xml:48:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.VIBRATE" />
31-->[:flutter_local_notifications] E:\Programs\task_swap\task_swap_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-66
31-->[:flutter_local_notifications] E:\Programs\task_swap\task_swap_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-63
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->[:flutter_local_notifications] E:\Programs\task_swap\task_swap_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-77
32-->[:flutter_local_notifications] E:\Programs\task_swap\task_swap_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-74
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.task_swap_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.task_swap_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\889ae3adf7a24645889ee22f4dad2cac\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="true"
45        android:icon="@mipmap/ic_launcher"
46        android:label="task_swap_app"
47        android:usesCleartextTraffic="true" >
48        <activity
49            android:name="com.example.task_swap_app.MainActivity"
50            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
51            android:exported="true"
52            android:hardwareAccelerated="true"
53            android:launchMode="singleTop"
54            android:taskAffinity=""
55            android:theme="@style/LaunchTheme"
56            android:windowSoftInputMode="adjustResize" >
57
58            <!--
59                 Specifies an Android theme to apply to this Activity as soon as
60                 the Android process has started. This theme is visible to the user
61                 while the Flutter UI initializes. After that, this theme continues
62                 to determine the Window background behind the Flutter UI.
63            -->
64            <meta-data
65                android:name="io.flutter.embedding.android.NormalTheme"
66                android:resource="@style/NormalTheme" />
67
68            <intent-filter>
69                <action android:name="android.intent.action.MAIN" />
70
71                <category android:name="android.intent.category.LAUNCHER" />
72            </intent-filter>
73        </activity>
74        <!--
75             Don't delete the meta-data below.
76             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
77        -->
78        <meta-data
79            android:name="flutterEmbedding"
80            android:value="2" />
81
82        <uses-library
82-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
83            android:name="androidx.window.extensions"
83-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
84            android:required="false" />
84-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
85        <uses-library
85-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
86            android:name="androidx.window.sidecar"
86-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
87            android:required="false" />
87-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8859fd2468ade3ef2655d1f63d6737f2\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
88
89        <provider
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
90            android:name="androidx.startup.InitializationProvider"
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
91            android:authorities="com.example.task_swap_app.androidx-startup"
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
92            android:exported="false" >
92-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
93            <meta-data
93-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
95                android:value="androidx.startup" />
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\40a1bd5ff2ce65cd1fad055cc690d356\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
96            <meta-data
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
97                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
98                android:value="androidx.startup" />
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
99        </provider>
100
101        <receiver
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
102            android:name="androidx.profileinstaller.ProfileInstallReceiver"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
103            android:directBootAware="false"
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
104            android:enabled="true"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
105            android:exported="true"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
106            android:permission="android.permission.DUMP" >
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
108                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
111                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
114                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
115            </intent-filter>
116            <intent-filter>
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
117                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\408a642162969e960684d9c16b2f116a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
118            </intent-filter>
119        </receiver>
120    </application>
121
122</manifest>
