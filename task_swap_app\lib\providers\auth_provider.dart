import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider with ChangeNotifier {
  AuthState _state = AuthState.initial;
  User? _user;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthState get state => _state;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _user != null;

  // Initialize auth state
  Future<void> initializeAuth() async {
    _setLoading(true);

    try {
      final isLoggedIn = await AuthService.isLoggedIn();

      if (isLoggedIn) {
        // Try to get stored user data
        final storedUser = await AuthService.getStoredUser();

        if (storedUser != null) {
          _user = storedUser;
          _setState(AuthState.authenticated);
        } else {
          // Try to fetch user profile from API
          final response = await AuthService.getProfile();

          if (response.success && response.data != null) {
            _user = response.data;
            _setState(AuthState.authenticated);
          } else {
            // Token might be invalid, logout
            await logout();
          }
        }
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('Failed to initialize authentication: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await AuthService.login(email, password);

      if (response.success && response.data != null) {
        _user = response.data;
        _setState(AuthState.authenticated);
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Login failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Login failed: $e');
      _setLoading(false);
      return false;
    }
  }

  // Register
  Future<bool> register({
    required String email,
    required String username,
    required String name,
    required String group,
    required String password,
    required String confirmPassword,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await AuthService.register(
        email: email,
        username: username,
        name: name,
        group: group,
        password: password,
        confirmPassword: confirmPassword,
      );

      if (response.success && response.data != null) {
        _user = response.data;
        _setState(AuthState.authenticated);
        _setLoading(false);
        return true;
      } else {
        _setError(response.message ?? 'Registration failed');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Registration failed: $e');
      _setLoading(false);
      return false;
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> data) async {
    _setLoading(true);
    _clearError();

    try {
      final response = await AuthService.updateProfile(data);

      if (response.success && response.data != null) {
        _user = response.data;
        _setLoading(false);
        notifyListeners();
        return true;
      } else {
        _setError(response.message ?? 'Failed to update profile');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError('Failed to update profile: $e');
      _setLoading(false);
      return false;
    }
  }

  // Refresh user profile
  Future<void> refreshProfile() async {
    if (!isAuthenticated) return;

    try {
      final response = await AuthService.getProfile();

      if (response.success && response.data != null) {
        _user = response.data;
        notifyListeners();
      }
    } catch (e) {
      // Silently fail for refresh operations
      debugPrint('Failed to refresh profile: $e');
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      await AuthService.logout();
      _user = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setError('Logout failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      return await AuthService.refreshToken();
    } catch (e) {
      debugPrint('Token refresh failed: $e');
      return false;
    }
  }

  // Private helper methods
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _state = AuthState.error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _state =
          _user != null ? AuthState.authenticated : AuthState.unauthenticated;
    }
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _clearError();
  }
}
