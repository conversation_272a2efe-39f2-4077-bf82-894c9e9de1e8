from rest_framework import generics, permissions, status
from rest_framework.response import Response
from django.db.models import Q
from .models import Task
from .serializers import TaskSerializer, TaskCreateUpdateSerializer, TaskListSerializer


class TaskListCreateView(generics.ListCreateAPIView):
    """List all tasks or create a new task"""
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TaskCreateUpdateSerializer
        return TaskListSerializer

    def get_queryset(self):
        user = self.request.user
        # Return tasks from the same group or assigned to the user
        return Task.objects.filter(
            Q(created_by__group=user.group) | Q(assigned_to=user)
        ).select_related('created_by', 'assigned_to')


class TaskDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Retrieve, update or delete a task"""
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return TaskCreateUpdateSerializer
        return TaskSerializer

    def get_queryset(self):
        user = self.request.user
        # Users can only access tasks they created or are assigned to
        return Task.objects.filter(
            Q(created_by=user) | Q(assigned_to=user)
        ).select_related('created_by', 'assigned_to')


class MyTasksView(generics.ListAPIView):
    """List tasks assigned to the current user"""
    serializer_class = TaskListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Task.objects.filter(
            assigned_to=self.request.user
        ).select_related('created_by', 'assigned_to')


class CreatedTasksView(generics.ListAPIView):
    """List tasks created by the current user"""
    serializer_class = TaskListSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Task.objects.filter(
            created_by=self.request.user
        ).select_related('created_by', 'assigned_to')
