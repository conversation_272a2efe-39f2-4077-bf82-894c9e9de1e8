import 'dart:io';
import 'package:http/http.dart' as http;
import 'constants.dart';

class NetworkTest {
  static Future<bool> testBackendConnection() async {
    try {
      print('Testing connection to: ${AppConstants.baseUrl}');
      
      final response = await http.get(
        Uri.parse('${AppConstants.baseUrl}/admin/'),
        headers: {'Accept': 'text/html,application/json'},
      ).timeout(const Duration(seconds: 10));
      
      print('Response status: ${response.statusCode}');
      print('Response headers: ${response.headers}');
      
      // Even a 404 or redirect means the server is reachable
      if (response.statusCode >= 200 && response.statusCode < 500) {
        print('✅ Backend server is reachable!');
        return true;
      } else {
        print('❌ Backend server returned status: ${response.statusCode}');
        return false;
      }
    } on SocketException catch (e) {
      print('❌ Socket Exception: $e');
      print('💡 Possible solutions:');
      print('   1. Make sure Django server is running on port 8000');
      print('   2. If using Android emulator, use 10.0.2.2 instead of 127.0.0.1');
      print('   3. If using iOS simulator, use 127.0.0.1');
      print('   4. Check firewall settings');
      return false;
    } on HttpException catch (e) {
      print('❌ HTTP Exception: $e');
      return false;
    } catch (e) {
      print('❌ Unknown error: $e');
      return false;
    }
  }
  
  static Future<void> testApiEndpoints() async {
    print('\n🔍 Testing API endpoints...');
    
    final endpoints = [
      '/api/register/',
      '/api/login/',
      '/api/tasks/',
    ];
    
    for (final endpoint in endpoints) {
      try {
        final url = '${AppConstants.baseUrl}$endpoint';
        print('Testing: $url');
        
        final response = await http.get(
          Uri.parse(url),
          headers: {'Accept': 'application/json'},
        ).timeout(const Duration(seconds: 5));
        
        print('  Status: ${response.statusCode}');
        
        // 401 (Unauthorized) is expected for protected endpoints
        if (response.statusCode == 401) {
          print('  ✅ Endpoint exists (requires authentication)');
        } else if (response.statusCode >= 200 && response.statusCode < 500) {
          print('  ✅ Endpoint is accessible');
        } else {
          print('  ❌ Unexpected status code');
        }
      } catch (e) {
        print('  ❌ Error: $e');
      }
    }
  }
  
  static Future<void> runFullNetworkTest() async {
    print('🚀 Starting network connectivity test...\n');
    
    final isConnected = await testBackendConnection();
    
    if (isConnected) {
      await testApiEndpoints();
    }
    
    print('\n📋 Network test completed!');
  }
}
