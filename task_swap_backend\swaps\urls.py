from django.urls import path
from . import views

urlpatterns = [
    path('', views.SwapRequestCreateView.as_view(), name='swap-create'),
    path('requests/', views.SwapRequestListView.as_view(), name='swap-requests'),
    path('incoming/', views.IncomingSwapRequestsView.as_view(), name='incoming-swaps'),
    path('outgoing/', views.OutgoingSwapRequestsView.as_view(), name='outgoing-swaps'),
    path('<int:pk>/respond/', views.respond_to_swap, name='swap-respond'),
]
