from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Admin configuration for custom User model"""
    list_display = ('email', 'username', 'name', 'group', 'points', 'is_active', 'date_joined')
    list_filter = ('is_active', 'is_staff', 'group', 'date_joined')
    search_fields = ('email', 'username', 'name')
    ordering = ('email',)

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('name', 'group', 'points')}),
    )
