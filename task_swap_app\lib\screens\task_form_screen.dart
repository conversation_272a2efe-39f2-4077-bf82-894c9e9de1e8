import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import '../providers/task_provider.dart';
import '../providers/auth_provider.dart';
import '../models/task_model.dart';
import '../utils/constants.dart';
import '../utils/validators.dart';

class TaskFormScreen extends StatefulWidget {
  final Task? task; // null for create, Task object for edit
  
  const TaskFormScreen({super.key, this.task});

  @override
  State<TaskFormScreen> createState() => _TaskFormScreenState();
}

class _TaskFormScreenState extends State<TaskFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  DateTime? _selectedDueDate;
  TaskPriority _selectedPriority = TaskPriority.medium;
  TaskStatus _selectedStatus = TaskStatus.pending;
  int? _assignedToId;
  
  bool get _isEditing => widget.task != null;

  @override
  void initState() {
    super.initState();
    if (_isEditing) {
      _populateFields();
    }
  }

  void _populateFields() {
    final task = widget.task!;
    _titleController.text = task.title;
    _descriptionController.text = task.description;
    _selectedDueDate = task.dueDate;
    _selectedPriority = task.priority;
    _selectedStatus = task.status;
    _assignedToId = task.assignedTo?.id;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDueDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDueDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (picked != null && picked != _selectedDueDate) {
      setState(() {
        _selectedDueDate = picked;
      });
    }
  }

  Future<void> _saveTask() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final taskProvider = Provider.of<TaskProvider>(context, listen: false);
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    bool success;
    
    if (_isEditing) {
      // Update existing task
      success = await taskProvider.updateTask(
        id: widget.task!.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        dueDate: _selectedDueDate,
        priority: _selectedPriority,
        status: _selectedStatus,
        assignedToId: _assignedToId,
      );
    } else {
      // Create new task
      success = await taskProvider.createTask(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        dueDate: _selectedDueDate,
        priority: _selectedPriority,
        status: _selectedStatus,
        assignedToId: _assignedToId ?? authProvider.user?.id,
      );
    }

    if (success) {
      if (mounted) {
        Fluttertoast.showToast(
          msg: _isEditing ? 'Task updated successfully' : 'Task created successfully',
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } else {
      if (mounted) {
        Fluttertoast.showToast(
          msg: taskProvider.errorMessage ?? 'Failed to save task',
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.red,
          textColor: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Task' : 'Create Task'),
        backgroundColor: const Color(AppColors.primaryColorValue),
        foregroundColor: Colors.white,
        actions: [
          Consumer<TaskProvider>(
            builder: (context, taskProvider, child) {
              return TextButton(
                onPressed: taskProvider.isLoading ? null : _saveTask,
                child: Text(
                  AppStrings.save,
                  style: TextStyle(
                    color: taskProvider.isLoading ? Colors.grey : Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<TaskProvider>(
        builder: (context, taskProvider, child) {
          return Stack(
            children: [
              SingleChildScrollView(
                padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Task Title
                      TextFormField(
                        controller: _titleController,
                        textInputAction: TextInputAction.next,
                        validator: Validators.validateTaskTitle,
                        decoration: InputDecoration(
                          labelText: AppStrings.taskTitle,
                          prefixIcon: const Icon(Icons.title),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: AppDimensions.paddingMedium),
                      
                      // Task Description
                      TextFormField(
                        controller: _descriptionController,
                        textInputAction: TextInputAction.newline,
                        validator: Validators.validateTaskDescription,
                        maxLines: 4,
                        decoration: InputDecoration(
                          labelText: AppStrings.taskDescription,
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                          ),
                          alignLabelWithHint: true,
                        ),
                      ),
                      
                      const SizedBox(height: AppDimensions.paddingMedium),
                      
                      // Due Date
                      InkWell(
                        onTap: _selectDueDate,
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: AppStrings.dueDate,
                            prefixIcon: const Icon(Icons.calendar_today),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                            ),
                          ),
                          child: Text(
                            _selectedDueDate != null
                                ? DateFormat('MMM dd, yyyy').format(_selectedDueDate!)
                                : 'Select due date (optional)',
                            style: TextStyle(
                              color: _selectedDueDate != null ? null : Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: AppDimensions.paddingMedium),
                      
                      // Priority
                      DropdownButtonFormField<TaskPriority>(
                        value: _selectedPriority,
                        decoration: InputDecoration(
                          labelText: AppStrings.priority,
                          prefixIcon: const Icon(Icons.flag),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                          ),
                        ),
                        items: TaskPriority.values.map((priority) {
                          return DropdownMenuItem(
                            value: priority,
                            child: Row(
                              children: [
                                Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    color: _getPriorityColor(priority),
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(priority.displayName),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedPriority = value;
                            });
                          }
                        },
                      ),
                      
                      const SizedBox(height: AppDimensions.paddingMedium),
                      
                      // Status (only show for editing)
                      if (_isEditing) ...[
                        DropdownButtonFormField<TaskStatus>(
                          value: _selectedStatus,
                          decoration: InputDecoration(
                            labelText: AppStrings.status,
                            prefixIcon: const Icon(Icons.info),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                            ),
                          ),
                          items: TaskStatus.values.map((status) {
                            return DropdownMenuItem(
                              value: status,
                              child: Row(
                                children: [
                                  Container(
                                    width: 12,
                                    height: 12,
                                    decoration: BoxDecoration(
                                      color: _getStatusColor(status),
                                      shape: BoxShape.circle,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(status.displayName),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedStatus = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: AppDimensions.paddingMedium),
                      ],
                      
                      const SizedBox(height: AppDimensions.paddingLarge),
                      
                      // Save Button (alternative to app bar button)
                      SizedBox(
                        height: AppDimensions.buttonHeight,
                        child: ElevatedButton.icon(
                          onPressed: taskProvider.isLoading ? null : _saveTask,
                          icon: const Icon(Icons.save),
                          label: Text(_isEditing ? 'Update Task' : 'Create Task'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(AppColors.primaryColorValue),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Loading overlay
              if (taskProvider.isLoading)
                Container(
                  color: Colors.black26,
                  child: const Center(
                    child: SpinKitThreeBounce(
                      color: Color(AppColors.primaryColorValue),
                      size: 30,
                    ),
                  ),
                ),
            ],
          );
        },
      ),
    );
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return const Color(AppColors.lowPriorityColor);
      case TaskPriority.medium:
        return const Color(AppColors.mediumPriorityColor);
      case TaskPriority.high:
        return const Color(AppColors.highPriorityColor);
      case TaskPriority.urgent:
        return const Color(AppColors.urgentPriorityColor);
    }
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return const Color(AppColors.pendingStatusColor);
      case TaskStatus.inProgress:
        return const Color(AppColors.inProgressStatusColor);
      case TaskStatus.completed:
        return const Color(AppColors.completedStatusColor);
      case TaskStatus.cancelled:
        return const Color(AppColors.cancelledStatusColor);
    }
  }
}
