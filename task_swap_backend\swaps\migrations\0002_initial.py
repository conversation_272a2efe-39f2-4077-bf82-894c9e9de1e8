# Generated by Django 5.2 on 2025-07-09 18:26

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('swaps', '0001_initial'),
        ('tasks', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='swaprequest',
            name='from_task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='swap_requests_from', to='tasks.task'),
        ),
    ]
