
# TaskSwap - Frontend Documentation (Flutter Mobile App)

## Overview
TaskSwap is a task-sharing mobile app that enables users to assign, swap, and manage tasks within a group (family or contacts). The frontend is built using Flutter, targeting Android and iOS devices. It connects to a Django REST Framework backend with JWT authentication.

## Project Structure
```
lib/
├── main.dart
├── screens/
│   ├── login_screen.dart
│   ├── dashboard_screen.dart
│   ├── task_form_screen.dart
│   ├── swap_center_screen.dart
│   ├── leaderboard_screen.dart
│   └── profile_screen.dart
├── widgets/
│   ├── task_card.dart
│   ├── swap_request_card.dart
│   └── custom_drawer.dart
├── models/
│   ├── user_model.dart
│   ├── task_model.dart
│   ├── swap_request_model.dart
│   └── api_response_model.dart
├── services/
│   ├── api_service.dart
│   ├── auth_service.dart
│   ├── task_service.dart
│   └── swap_service.dart
├── providers/
│   ├── auth_provider.dart
│   ├── task_provider.dart
│   └── swap_provider.dart
└── utils/
    ├── constants.dart
    ├── validators.dart
    └── api_endpoints.dart
```

## Core UI Screens

### Authentication Screens
- **Login Screen**: Email and password authentication
- **Registration Screen**: Email, username, name, group, password, confirm password
- **Profile Screen**: View and edit user profile (name, group, points display)

### Main App Screens
- **Dashboard**: Bottom navigation with multiple tabs
  - My Tasks: Tasks assigned to current user
  - Group Tasks: All tasks from user's group
  - Created Tasks: Tasks created by current user
- **Task Form Screen**: Create/edit tasks with title, description, assignee, due date, priority, status
- **Task Detail Screen**: View full task details with edit/delete options
- **Swap Center**:
  - Incoming swap requests (accept/decline)
  - Outgoing swap requests (view status)
  - Create new swap requests
- **Leaderboard**: Show points earned by group members
- **Calendar (optional)**: Display task deadlines and due dates

## State Management
- **Recommended**: Provider or Riverpod for state management
- **Auth State**: User authentication status, tokens, user profile
- **Task State**: Task lists, filters, CRUD operations
- **Swap State**: Swap requests, notifications, status updates
- **Global State**: Loading states, error handling, notifications

## API Communication
- **Base URL**: `http://127.0.0.1:8000` (development) / production URL
- **Authentication**: JWT tokens (access + refresh)
- **Token Storage**: Secure storage using `flutter_secure_storage`
- **Token Refresh**: Automatic refresh when access token expires
- **Error Handling**: HTTP status codes, validation errors, network errors
- **Request Headers**: Authorization Bearer token, Content-Type application/json

## API Endpoints Used

### Authentication Endpoints
- **POST** `/api/register/` - User registration
- **POST** `/api/login/` - User login
- **POST** `/api/token/refresh/` - Refresh access token
- **GET/PUT** `/api/profile/` - Get/update user profile

### Task Management Endpoints
- **GET** `/api/tasks/` - List group tasks and assigned tasks
- **POST** `/api/tasks/` - Create new task
- **GET** `/api/tasks/<id>/` - Get task details
- **PUT/PATCH** `/api/tasks/<id>/` - Update task
- **DELETE** `/api/tasks/<id>/` - Delete task
- **GET** `/api/tasks/my-tasks/` - Get tasks assigned to current user
- **GET** `/api/tasks/created-tasks/` - Get tasks created by current user

### Swap Management Endpoints
- **POST** `/api/swap/` - Create swap request
- **GET** `/api/swap/requests/` - Get all swap requests
- **GET** `/api/swap/incoming/` - Get incoming swap requests
- **GET** `/api/swap/outgoing/` - Get outgoing swap requests
- **POST** `/api/swap/<id>/respond/` - Accept/decline swap request

## Required Flutter Packages

### Core Dependencies
```yaml
dependencies:
  flutter:
    sdk: flutter
  http: ^1.1.0                    # API calls
  flutter_secure_storage: ^9.0.0  # Secure token storage
  provider: ^6.1.1                # State management
  intl: ^0.19.0                   # Date formatting
  flutter_local_notifications: ^17.0.0  # Push notifications
  shared_preferences: ^2.2.2      # App preferences
```

### UI & UX Packages
```yaml
  cupertino_icons: ^1.0.6         # iOS style icons
  flutter_spinkit: ^5.2.0         # Loading animations
  fluttertoast: ^8.2.4           # Toast messages
  cached_network_image: ^3.3.0    # Image caching
  pull_to_refresh: ^2.0.0         # Pull to refresh
```

### Development Dependencies
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0          # Linting rules
  build_runner: ^2.4.7           # Code generation
```

## Data Models

### User Model
```dart
class User {
  final int id;
  final String email;
  final String username;
  final String name;
  final String group;
  final int points;
  final DateTime dateJoined;

  // Constructor, fromJson, toJson methods
}
```

### Task Model
```dart
class Task {
  final int id;
  final String title;
  final String description;
  final User? createdBy;
  final User? assignedTo;
  final DateTime? dueDate;
  final TaskPriority priority;
  final TaskStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Constructor, fromJson, toJson methods
}

enum TaskPriority { low, medium, high, urgent }
enum TaskStatus { pending, inProgress, completed, cancelled }
```

### SwapRequest Model
```dart
class SwapRequest {
  final int id;
  final Task fromTask;
  final Task toTask;
  final User fromUser;
  final User toUser;
  final SwapStatus status;
  final String? message;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Constructor, fromJson, toJson methods
}

enum SwapStatus { pending, accepted, declined, cancelled }
```

## Key Features Implementation

### Authentication Flow
1. **Login/Register**: Email-based authentication
2. **Token Management**: Store JWT tokens securely
3. **Auto-refresh**: Refresh tokens automatically
4. **Logout**: Clear tokens and redirect to login

### Task Management
1. **Task Lists**: Separate views for my tasks, group tasks, created tasks
2. **Task CRUD**: Create, read, update, delete operations
3. **Task Filtering**: Filter by status, priority, assignee
4. **Task Assignment**: Assign tasks to group members

### Swap System
1. **Create Swaps**: Select tasks to swap
2. **Manage Requests**: View incoming/outgoing requests
3. **Accept/Decline**: Respond to swap requests
4. **Notifications**: Alert users of new swap requests

### UI/UX Considerations
- **Loading States**: Show spinners during API calls
- **Error Handling**: Display user-friendly error messages
- **Offline Support**: Cache data for offline viewing
- **Pull to Refresh**: Refresh data with pull gesture
- **Search & Filter**: Find tasks quickly
- **Dark Mode**: Support system theme preferences

## API Service Implementation Example

### Base API Service
```dart
class ApiService {
  static const String baseUrl = 'http://127.0.0.1:8000';
  static const String apiUrl = '$baseUrl/api';

  static Future<Map<String, String>> _getHeaders() async {
    final token = await AuthService.getAccessToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  static Future<http.Response> get(String endpoint) async {
    final headers = await _getHeaders();
    return http.get(Uri.parse('$apiUrl$endpoint'), headers: headers);
  }

  static Future<http.Response> post(String endpoint, Map<String, dynamic> data) async {
    final headers = await _getHeaders();
    return http.post(
      Uri.parse('$apiUrl$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    );
  }

  // PUT, PATCH, DELETE methods...
}
```

### Authentication Service
```dart
class AuthService {
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  static Future<bool> login(String email, String password) async {
    try {
      final response = await ApiService.post('/login/', {
        'email': email,
        'password': password,
      });

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        await _storeTokens(data['access'], data['refresh']);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Future<void> _storeTokens(String access, String refresh) async {
    const storage = FlutterSecureStorage();
    await storage.write(key: _accessTokenKey, value: access);
    await storage.write(key: _refreshTokenKey, value: refresh);
  }

  // Token refresh, logout methods...
}
```

## Development Setup

### Prerequisites
- Flutter SDK (latest stable version)
- Android Studio / VS Code with Flutter extensions
- Android emulator or physical device
- Backend server running at `http://127.0.0.1:8000`

### Project Setup
```bash
# Create new Flutter project
flutter create taskswap_app
cd taskswap_app

# Add dependencies to pubspec.yaml
flutter pub get

# Run the app
flutter run
```

### Environment Configuration
```dart
// lib/utils/constants.dart
class AppConstants {
  static const String baseUrl = 'http://127.0.0.1:8000';  // Development
  // static const String baseUrl = 'https://your-api.com';  // Production

  static const String apiUrl = '$baseUrl/api';

  // App constants
  static const String appName = 'TaskSwap';
  static const int requestTimeout = 30; // seconds
}
```

## Testing Strategy

### Unit Tests
- Model serialization/deserialization
- API service methods
- Business logic functions
- Validation methods

### Widget Tests
- Screen rendering
- User interactions
- Form validations
- Navigation flows

### Integration Tests
- End-to-end user flows
- API integration
- Authentication flow
- Task management flow

## Deployment

### Android
1. Build APK: `flutter build apk --release`
2. Build App Bundle: `flutter build appbundle --release`
3. Upload to Google Play Store

### iOS
1. Build iOS: `flutter build ios --release`
2. Archive in Xcode
3. Upload to App Store Connect

### Configuration for Production
- Update API base URL
- Configure app signing
- Set up proper app icons and splash screens
- Configure push notification certificates
- Test on physical devices
