import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_model.dart';
import '../models/api_response_model.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class AuthService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  // Login user
  static Future<ApiResponse<User>> login(String email, String password) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.login,
        {
          'email': email,
          'password': password,
        },
        includeAuth: false,
      );

      if (response.success && response.data != null) {
        final data = response.data!;

        // Store tokens
        if (data['access'] != null && data['refresh'] != null) {
          await _storeTokens(data['access'], data['refresh']);
        }

        // Store user data if available
        if (data['user'] != null) {
          final user = User.fromJson(data['user']);
          await _storeUserData(user);

          return ApiResponse.success(
            data: user,
            message: response.message ?? 'Login successful',
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse.error(
        message: response.message ?? 'Login failed',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Login failed: $e',
        statusCode: 500,
      );
    }
  }

  // Register user
  static Future<ApiResponse<User>> register({
    required String email,
    required String username,
    required String name,
    required String group,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.register,
        {
          'email': email,
          'username': username,
          'name': name,
          'group': group,
          'password': password,
          'password_confirm':
              confirmPassword, // Match Django backend expectation
        },
        includeAuth: false,
      );

      if (response.success && response.data != null) {
        final data = response.data!;

        // Store tokens if provided
        if (data['access'] != null && data['refresh'] != null) {
          await _storeTokens(data['access'], data['refresh']);
        }

        // Store user data if available
        if (data['user'] != null) {
          final user = User.fromJson(data['user']);
          await _storeUserData(user);

          return ApiResponse.success(
            data: user,
            message: response.message ?? 'Registration successful',
            statusCode: response.statusCode,
          );
        }
      }

      return ApiResponse.error(
        message: response.message ?? 'Registration failed',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Registration failed: $e',
        statusCode: 500,
      );
    }
  }

  // Get current user profile
  static Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiEndpoints.profile,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final user = User.fromJson(response.data!);
        await _storeUserData(user);

        return ApiResponse.success(
          data: user,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get profile',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get profile: $e',
        statusCode: 500,
      );
    }
  }

  // Update user profile
  static Future<ApiResponse<User>> updateProfile(
      Map<String, dynamic> data) async {
    try {
      final response = await ApiService.put<Map<String, dynamic>>(
        ApiEndpoints.profile,
        data,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final user = User.fromJson(response.data!);
        await _storeUserData(user);

        return ApiResponse.success(
          data: user,
          message: response.message ?? 'Profile updated successfully',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to update profile',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to update profile: $e',
        statusCode: 500,
      );
    }
  }

  // Refresh access token
  static Future<bool> refreshToken() async {
    try {
      final refreshToken =
          await _storage.read(key: AppConstants.refreshTokenKey);
      if (refreshToken == null) return false;

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.tokenRefresh,
        {'refresh': refreshToken},
        includeAuth: false,
      );

      if (response.success && response.data != null) {
        final data = response.data!;
        if (data['access'] != null) {
          await _storage.write(
              key: AppConstants.accessTokenKey, value: data['access']);
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  // Logout user
  static Future<void> logout() async {
    await ApiService.clearTokens();
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await _storage.read(key: AppConstants.accessTokenKey);
    return token != null;
  }

  // Get stored access token
  static Future<String?> getAccessToken() async {
    return await _storage.read(key: AppConstants.accessTokenKey);
  }

  // Get stored refresh token
  static Future<String?> getRefreshToken() async {
    return await _storage.read(key: AppConstants.refreshTokenKey);
  }

  // Get stored user data
  static Future<User?> getStoredUser() async {
    try {
      final userData = await _storage.read(key: AppConstants.userDataKey);
      if (userData != null) {
        final Map<String, dynamic> userJson = jsonDecode(userData);
        return User.fromJson(userJson);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Store tokens securely
  static Future<void> _storeTokens(
      String accessToken, String refreshToken) async {
    await _storage.write(key: AppConstants.accessTokenKey, value: accessToken);
    await _storage.write(
        key: AppConstants.refreshTokenKey, value: refreshToken);
  }

  // Store user data securely
  static Future<void> _storeUserData(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _storage.write(key: AppConstants.userDataKey, value: userJson);
  }
}
