from rest_framework import serializers
from .models import Task
from users.serializers import UserProfileSerializer


class TaskSerializer(serializers.ModelSerializer):
    """Serializer for Task model"""
    created_by = UserProfileSerializer(read_only=True)
    assigned_to = UserProfileSerializer(read_only=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'description', 'created_by', 'assigned_to', 
            'assigned_to_id', 'due_date', 'priority', 'status', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ('id', 'created_by', 'created_at', 'updated_at')
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TaskCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating tasks"""
    
    class Meta:
        model = Task
        fields = [
            'title', 'description', 'assigned_to', 'due_date', 
            'priority', 'status'
        ]
    
    def create(self, validated_data):
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TaskListSerializer(serializers.ModelSerializer):
    """Simplified serializer for task lists"""
    created_by_name = serializers.CharField(source='created_by.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.name', read_only=True)
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'description', 'created_by_name', 
            'assigned_to_name', 'due_date', 'priority', 'status', 
            'created_at'
        ]
