from django.contrib import admin
from .models import Task


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    """Admin configuration for Task model"""
    list_display = ('title', 'created_by', 'assigned_to', 'priority', 'status', 'due_date', 'created_at')
    list_filter = ('priority', 'status', 'created_at', 'due_date')
    search_fields = ('title', 'description', 'created_by__email', 'assigned_to__email')
    list_editable = ('priority', 'status')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)

    fieldsets = (
        (None, {
            'fields': ('title', 'description')
        }),
        ('Assignment', {
            'fields': ('created_by', 'assigned_to')
        }),
        ('Details', {
            'fields': ('priority', 'status', 'due_date')
        }),
    )

    readonly_fields = ('created_at', 'updated_at')
