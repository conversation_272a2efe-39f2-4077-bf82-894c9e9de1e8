import 'task_model.dart';
import 'user_model.dart';

enum SwapStatus { pending, accepted, declined, cancelled }

extension SwapStatusExtension on SwapStatus {
  String get displayName {
    switch (this) {
      case SwapStatus.pending:
        return 'Pending';
      case SwapStatus.accepted:
        return 'Accepted';
      case SwapStatus.declined:
        return 'Declined';
      case SwapStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get value {
    switch (this) {
      case SwapStatus.pending:
        return 'pending';
      case SwapStatus.accepted:
        return 'accepted';
      case SwapStatus.declined:
        return 'declined';
      case SwapStatus.cancelled:
        return 'cancelled';
    }
  }

  static SwapStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return SwapStatus.pending;
      case 'accepted':
        return SwapStatus.accepted;
      case 'declined':
        return SwapStatus.declined;
      case 'cancelled':
        return SwapStatus.cancelled;
      default:
        return SwapStatus.pending;
    }
  }
}

class SwapRequest {
  final int id;
  final Task fromTask;
  final Task toTask;
  final User fromUser;
  final User toUser;
  final SwapStatus status;
  final String? message;
  final DateTime createdAt;
  final DateTime updatedAt;

  SwapRequest({
    required this.id,
    required this.fromTask,
    required this.toTask,
    required this.fromUser,
    required this.toUser,
    required this.status,
    this.message,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SwapRequest.fromJson(Map<String, dynamic> json) {
    return SwapRequest(
      id: json['id'] as int,
      fromTask: Task.fromJson(json['from_task'] as Map<String, dynamic>),
      toTask: Task.fromJson(json['to_task'] as Map<String, dynamic>),
      fromUser: User.fromJson(json['from_user'] as Map<String, dynamic>),
      toUser: User.fromJson(json['to_user'] as Map<String, dynamic>),
      status: SwapStatusExtension.fromString(json['status'] as String),
      message: json['message'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'from_task': fromTask.toJson(),
      'to_task': toTask.toJson(),
      'from_user': fromUser.toJson(),
      'to_user': toUser.toJson(),
      'status': status.value,
      'message': message,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  SwapRequest copyWith({
    int? id,
    Task? fromTask,
    Task? toTask,
    User? fromUser,
    User? toUser,
    SwapStatus? status,
    String? message,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SwapRequest(
      id: id ?? this.id,
      fromTask: fromTask ?? this.fromTask,
      toTask: toTask ?? this.toTask,
      fromUser: fromUser ?? this.fromUser,
      toUser: toUser ?? this.toUser,
      status: status ?? this.status,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SwapRequest(id: $id, fromTask: ${fromTask.title}, toTask: ${toTask.title}, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SwapRequest && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
