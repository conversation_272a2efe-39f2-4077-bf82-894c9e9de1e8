import '../models/swap_request_model.dart';
import '../models/api_response_model.dart';
import '../utils/constants.dart';
import 'api_service.dart';

class SwapService {
  // Get all swap requests
  static Future<ApiResponse<List<SwapRequest>>> getAllSwapRequests() async {
    try {
      final response = await ApiService.get<List<Map<String, dynamic>>>(
        ApiEndpoints.swapRequests,
      );

      if (response.success && response.data != null) {
        final swapRequests = (response.data as List<dynamic>)
            .map((swapJson) =>
                SwapRequest.fromJson(swapJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: swapRequests,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get swap requests',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get swap requests: $e',
        statusCode: 500,
      );
    }
  }

  // Get incoming swap requests
  static Future<ApiResponse<List<SwapRequest>>>
      getIncomingSwapRequests() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiEndpoints.incomingSwaps,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final results = response.data!['results'] as List<dynamic>;
        final swapRequests = results
            .map((swapJson) =>
                SwapRequest.fromJson(swapJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: swapRequests,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get incoming swap requests',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get incoming swap requests: $e',
        statusCode: 500,
      );
    }
  }

  // Get outgoing swap requests
  static Future<ApiResponse<List<SwapRequest>>>
      getOutgoingSwapRequests() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        ApiEndpoints.outgoingSwaps,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        final results = response.data!['results'] as List<dynamic>;
        final swapRequests = results
            .map((swapJson) =>
                SwapRequest.fromJson(swapJson as Map<String, dynamic>))
            .toList();

        return ApiResponse.success(
          data: swapRequests,
          message: response.message,
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to get outgoing swap requests',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to get outgoing swap requests: $e',
        statusCode: 500,
      );
    }
  }

  // Create new swap request
  static Future<ApiResponse<SwapRequest?>> createSwapRequest({
    required int fromTaskId,
    required int toTaskId,
    String? message,
  }) async {
    try {
      final data = <String, dynamic>{
        'from_task_id': fromTaskId,
        'to_task_id': toTaskId,
      };

      if (message != null && message.isNotEmpty) {
        data['message'] = message;
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.swap,
        data,
        fromJson: (json) => json,
      );

      if (response.success) {
        // Backend returns basic data on creation, we'll return a success without the full object
        // The UI should refresh the lists to get the complete data
        return ApiResponse.success(
          data: null,
          message: response.message ?? 'Swap request created successfully',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to create swap request',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to create swap request: $e',
        statusCode: 500,
      );
    }
  }

  // Respond to swap request (accept or decline)
  static Future<ApiResponse<SwapRequest>> respondToSwapRequest({
    required int swapRequestId,
    required bool accept,
  }) async {
    try {
      final data = {
        'action': accept ? 'accept' : 'decline',
      };

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.swapRespond(swapRequestId),
        data,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        // Backend returns {message: "...", swap_request: {...}}
        final swapRequestData =
            response.data!['swap_request'] as Map<String, dynamic>;
        final swapRequest = SwapRequest.fromJson(swapRequestData);

        return ApiResponse.success(
          data: swapRequest,
          message: response.data!['message'] as String? ??
              (accept ? 'Swap request accepted' : 'Swap request declined'),
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to respond to swap request',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to respond to swap request: $e',
        statusCode: 500,
      );
    }
  }

  // Accept swap request
  static Future<ApiResponse<SwapRequest>> acceptSwapRequest(
      int swapRequestId) async {
    return respondToSwapRequest(swapRequestId: swapRequestId, accept: true);
  }

  // Decline swap request
  static Future<ApiResponse<SwapRequest>> declineSwapRequest(
      int swapRequestId) async {
    return respondToSwapRequest(swapRequestId: swapRequestId, accept: false);
  }

  // Cancel swap request (for outgoing requests)
  static Future<ApiResponse<SwapRequest>> cancelSwapRequest(
      int swapRequestId) async {
    try {
      final data = {
        'action': 'cancel',
      };

      final response = await ApiService.post<Map<String, dynamic>>(
        ApiEndpoints.swapRespond(swapRequestId),
        data,
        fromJson: (json) => json,
      );

      if (response.success && response.data != null) {
        // Backend returns {message: "...", swap_request: {...}}
        final swapRequestData =
            response.data!['swap_request'] as Map<String, dynamic>;
        final swapRequest = SwapRequest.fromJson(swapRequestData);

        return ApiResponse.success(
          data: swapRequest,
          message:
              response.data!['message'] as String? ?? 'Swap request cancelled',
          statusCode: response.statusCode,
        );
      }

      return ApiResponse.error(
        message: response.message ?? 'Failed to cancel swap request',
        errors: response.errors,
        statusCode: response.statusCode,
      );
    } catch (e) {
      return ApiResponse.error(
        message: 'Failed to cancel swap request: $e',
        statusCode: 500,
      );
    }
  }
}
