from django.db import models
from django.conf import settings


class SwapRequest(models.Model):
    """Model for task swap requests"""

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('accepted', 'Accepted'),
        ('declined', 'Declined'),
        ('cancelled', 'Cancelled'),
    ]

    from_task = models.ForeignKey(
        'tasks.Task',
        on_delete=models.CASCADE,
        related_name='swap_requests_from'
    )
    to_task = models.ForeignKey(
        'tasks.Task',
        on_delete=models.CASCADE,
        related_name='swap_requests_to'
    )
    from_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='swap_requests_sent'
    )
    to_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='swap_requests_received'
    )
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    message = models.TextField(blank=True, help_text="Optional message for the swap request")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['from_task', 'to_task', 'from_user', 'to_user']

    def __str__(self):
        return f"Swap: {self.from_task.title} <-> {self.to_task.title}"

    def accept(self):
        """Accept the swap request and exchange task assignments"""
        if self.status == 'pending':
            # Swap the task assignments
            original_from_assigned = self.from_task.assigned_to
            original_to_assigned = self.to_task.assigned_to

            self.from_task.assigned_to = original_to_assigned
            self.to_task.assigned_to = original_from_assigned

            self.from_task.save()
            self.to_task.save()

            self.status = 'accepted'
            self.save()

    def decline(self):
        """Decline the swap request"""
        if self.status == 'pending':
            self.status = 'declined'
            self.save()
