import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🧪 Testing Task Creation Frontend-Backend Integration...\n');
  
  const String baseUrl = 'http://192.168.31.244:8000';
  const String apiUrl = '$baseUrl/api';
  
  // Step 1: Login to get access token
  print('1️⃣ Logging in to get access token...');
  try {
    final loginResponse = await http.post(
      Uri.parse('$apiUrl/login/'),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'email': '<EMAIL>',
        'password': 'testpass123',
      }),
    );
    
    if (loginResponse.statusCode == 200) {
      final loginData = jsonDecode(loginResponse.body);
      final accessToken = loginData['access'];
      print('   ✅ Login successful');
      
      // Step 2: Test task creation
      print('\n2️⃣ Testing task creation...');
      final createTaskResponse = await http.post(
        Uri.parse('$apiUrl/tasks/'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $accessToken',
        },
        body: jsonEncode({
          'title': 'Test Task from Flutter',
          'description': 'This task was created from Flutter frontend',
          'priority': 'high',
          'status': 'pending',
          'assigned_to': 2, // Assign to user ID 2
        }),
      );
      
      print('   Response Status: ${createTaskResponse.statusCode}');
      print('   Response Body: ${createTaskResponse.body}');
      
      if (createTaskResponse.statusCode == 200 || createTaskResponse.statusCode == 201) {
        print('   ✅ Task creation successful!');
        
        final taskData = jsonDecode(createTaskResponse.body);
        print('   Created Task: ${taskData['title']}');
        
        // Step 3: Test task list retrieval
        print('\n3️⃣ Testing task list retrieval...');
        final listResponse = await http.get(
          Uri.parse('$apiUrl/tasks/'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $accessToken',
          },
        );
        
        print('   List Response Status: ${listResponse.statusCode}');
        if (listResponse.statusCode == 200) {
          final listData = jsonDecode(listResponse.body);
          print('   ✅ Task list retrieved successfully!');
          print('   Total tasks: ${listData['count']}');
          
          if (listData['results'] != null && listData['results'].isNotEmpty) {
            final firstTask = listData['results'][0];
            print('   First task: ${firstTask['title']}');
            print('   Priority: ${firstTask['priority']}');
            print('   Status: ${firstTask['status']}');
          }
        } else {
          print('   ❌ Failed to retrieve task list');
          print('   Response: ${listResponse.body}');
        }
        
      } else {
        print('   ❌ Task creation failed');
        print('   Response: ${createTaskResponse.body}');
      }
      
    } else {
      print('   ❌ Login failed: ${loginResponse.statusCode}');
      print('   Response: ${loginResponse.body}');
    }
    
  } catch (e) {
    print('   ❌ Error: $e');
  }
  
  print('\n📋 Task creation test completed!');
}
