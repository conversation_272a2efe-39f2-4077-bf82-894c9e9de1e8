from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.db.models import Q
from .models import User
from .serializers import UserRegistrationSerializer, UserLoginSerializer, UserProfileSerializer


@api_view(['POST'])
@permission_classes([AllowAny])
def register(request):
    """User registration endpoint"""
    print(f"Registration request data: {request.data}")
    print(f"Request content type: {request.content_type}")

    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        refresh = RefreshToken.for_user(user)
        return Response({
            'user': UserProfileSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_201_CREATED)

    print(f"Serializer errors: {serializer.errors}")
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def login(request):
    """User login endpoint"""
    serializer = UserLoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        refresh = RefreshToken.for_user(user)
        return Response({
            'user': UserProfileSerializer(user).data,
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UserProfileView(generics.RetrieveUpdateAPIView):
    """User profile view for authenticated users"""
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def leaderboard(request):
    """Get leaderboard of users sorted by points"""
    # Get current user's group
    user_group = request.user.group

    # Get users from the same group, ordered by points (descending)
    if user_group:
        users = User.objects.filter(group=user_group).order_by('-points', 'name')
    else:
        # If user has no group, show all users
        users = User.objects.all().order_by('-points', 'name')

    # Limit to top 50 users
    users = users[:50]

    # Serialize user data for leaderboard
    leaderboard_data = []
    for index, user in enumerate(users, 1):
        leaderboard_data.append({
            'rank': index,
            'id': user.id,
            'name': user.name or user.username,
            'username': user.username,
            'group': user.group,
            'points': user.points,
            'is_current_user': user.id == request.user.id,
        })

    return Response({
        'leaderboard': leaderboard_data,
        'current_user_rank': next(
            (item['rank'] for item in leaderboard_data if item['is_current_user']),
            None
        ),
        'total_users': len(leaderboard_data),
    }, status=status.HTTP_200_OK)
